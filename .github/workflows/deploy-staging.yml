name: Deploy to Staging

run-name: ${{ github.event.head_commit.message }}

on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      branch:
        description: "Branch to run on"
        required: true
        default: develop

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

env:
  APP_DEPLOYMENT_FILE: deployment/k8s/staging/deployment.yml
  APP_INGRESS_FILE: deployment/k8s/staging/ingress.yml
  REDIS_DEPLOYMENT_FILE: deployment/k8s/staging/redis
  DOPPLER_TOKEN: ${{ secrets.STAGING_DOPPLER_TOKEN }}
  DOPPLER_CONFIG: staging
  # NOTE(real-z-r2): For now, we will just use develop-latest as the tag.
  #
  # We should properly bump the alpha version here and use the actual version as the tag.
  DOCKER_IMAGE: registry.digitalocean.com/${{ vars.DIGITAL_OCEAN_REGISTRY_NAME }}/online-ordering:latest-develop-${{ github.sha }}

jobs:
  # This builds a docker image and uploads it to DO docker registry.
  build-push:
    runs-on: ubuntu-v22.04-c16-m64
    outputs:
      NEW_TAG: latest-develop-${{ github.sha }}
    steps:
      # setup
      - name: Checkout
        uses: actions/checkout@v3
      - name: Log in to Docker Hub
        run: echo "${{ secrets.DOCKER_HUB_TOKEN }}" | docker login -u "${{ secrets.DOCKER_HUB_USER }}" --password-stdin
      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITAL_OCEAN_KUBERNETES_TOKEN }}

      # Generate version file
      - name: Generate version file
        run: |
          echo "export const APP_VERSION = '${GITHUB_SHA}';" > src/version.js
          cat src/version.js

      # build and push online ordering container
      - name: Build online ordering container image
        run: |
          docker build --build-arg DOPPLER_TOKEN=${{ env.DOPPLER_TOKEN }} \
            --build-arg DOPPLER_CONFIG=${{ env.DOPPLER_CONFIG }} \
            -t ${{ env.DOCKER_IMAGE }} -f Dockerfile .
      - name: Log in to DigitalOcean Container Registry
        run: doctl registry login --expiry-seconds 1200
      - name: Push online ordering container image to DigitalOcean Container Registry
        run: docker push ${{ env.DOCKER_IMAGE }}

  # This takes the docker image (from the DO docker registry) and deploys it to the DO kubernetes cluster.
  deploy:
    runs-on: ubuntu-v22.04-c16-m64
    needs: build-push
    env:
      DOCKER_IMAGE: registry.digitalocean.com/${{ vars.DIGITAL_OCEAN_REGISTRY_NAME }}/online-ordering:${{ needs.build-push.outputs.NEW_TAG }}
    steps:
      # setup
      - name: Checkout
        uses: actions/checkout@v3
      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITAL_OCEAN_KUBERNETES_TOKEN }}
      - name: Setup Doppler CLI
        uses: ./.github/actions/setup-doppler

      # deploy online ordering to Kubernetes
      - name: Update Online Ordering deployment file
        run: sed -i 's|<IMAGE>|${{ env.DOCKER_IMAGE }}|' ${{ env.APP_DEPLOYMENT_FILE }}
      - name: Save DigitalOcean kubeconfig with short-lived credentials
        # This gets a kubeconfig with short-lived credentials.
        #
        # We need such credentials to deploy to the DO kubernetes cluster.
        run: doctl kubernetes cluster kubeconfig save --expiry-seconds 600 ${{ vars.STAGING_DIGITAL_OCEAN_CLUSTER_NAME }}
      - name: Deploy Kubernetes Secrets
        run: |
          # This secret is consumed in the deployment.yml file, under the env section.
          kubectl create secret generic doppler-staging-token \
            --from-literal=DOPPLER_TOKEN=${{ env.DOPPLER_TOKEN }} \
            --dry-run=client -o yaml | kubectl apply -f -
      - name: Deploy Online Ordering to DigitalOcean Kubernetes
        # Send the deployment file to the DO kubernetes cluster.
        run: |
          doppler run -- bash -c \
            'kubectl create secret generic redis-auth \
              --from-literal=password="${REDIS_PASSWORD}" \
              --dry-run=client -o yaml' \
            | kubectl apply -f -
            kubectl apply -f ${{ env.APP_DEPLOYMENT_FILE }}
            kubectl apply -f ${{ env.APP_INGRESS_FILE }}
            kubectl apply -f ${{ env.REDIS_DEPLOYMENT_FILE }}
      - name: Verify Online Ordering deployment
        # Wait for the deployment to be ready.
        run: kubectl rollout status deployment/online-ordering
