name: Restart production
on:
  workflow_dispatch:
    inputs:
      branch:
        description: "Branch to run on"
        required: true
        default: main

jobs:
  restart:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITAL_OCEAN_KUBERNETES_TOKEN }}
      - name: Setup Doppler CLI
        uses: ./.github/actions/setup-doppler
      - name: Save DigitalOcean kubeconfig with short-lived credentials
        # This gets a kubeconfig with short-lived credentials.
        #
        # We need such credentials to deploy to the DO kubernetes cluster.
        run: doctl kubernetes cluster kubeconfig save --expiry-seconds 600 ${{ vars.PRODUCTION_DIGITAL_OCEAN_CLUSTER_NAME }}
      - name: Restart Online Ordering
        run: |
          doppler run --bash -c \
            'kubectl create secret generic redis-auth \
              --from-literal=password="${REDIS_PASSWORD}" \
              --dry-run=client -o yaml' \
            | kubectl rollout restart deployment/online-ordering
