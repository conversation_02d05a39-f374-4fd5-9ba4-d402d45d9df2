name: Run tests

on:
  push:
    branches:
      - "**"

jobs:
  online-ordering-types-and-unit-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    env:
      DOPPLER_TOKEN: ${{ secrets.DEV_DOPPLER_TOKEN }}
    # needs:
    #   - build-and-test
    steps:
      - uses: actions/checkout@v4

      - name: Setup Doppler CLI
        uses: ./.github/actions/setup-doppler

      - name: Install dependencies
        run: npm ci

      - name: TypeScript and Svelte Check
        run: npm run check

      - name: Lint
        run: npm run lint

      - name: Unit Tests
        run: npm run test:unit

  online-ordering-integration-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    env:
      DOPPLER_TOKEN: ${{ secrets.DEV_DOPPLER_TOKEN }}
    steps:
      - uses: actions/checkout@v4

      - name: Setup Doppler CLI
        uses: ./.github/actions/setup-doppler

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright Browsers
        run: npx playwright install --with-deps

      - name: Run Playwright tests
        run: npx playwright test --reporter=html
        timeout-minutes: 20

      - name: Upload Playwright report
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 10
