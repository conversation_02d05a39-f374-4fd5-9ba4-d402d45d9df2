# Cloud Online Ordering

## Repo migration

This repo was migrated from `hyperion` around May 15th, 2025. Some PR links may appear broken, but can be found in the [`hyperion` repo](https://github.com/Round2POS/hyperion).

## Common commands

```bash
# Initialize the DO CLI, you will need a DO account and then generate a personal
# access token (PAT); the command will give you the URL and prompt you for the
# PAT.
doctl auth init

# Run this to get the k8s creds:
doctl kubernetes cluster kubeconfig save --expiry-seconds 600 r2-pos-staging-k8s01

# See all pods
kubectl get pods

# See logs for our pod
kubectl logs online-ordering-7b6c7b8758-9dt9p

# List all deployments.
kubectl get deployments

# After doppler environment variables are updated, restart the deployment to
# pick up the new env vars
kubectl rollout restart deployment online-ordering

# See/wait for the deployment to be ready
kubectl rollout status deployment/online-ordering
```
