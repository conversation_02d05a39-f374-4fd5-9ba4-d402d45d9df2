<script>
  import { page } from "$app/stores";
</script>

<div class="error-container">
  {#if $page.status}
    <h1 class="status-code">{$page.status}</h1>
  {/if}

  {#if $page.error?.message}
    <p class="error-message">{$page.error.message}</p>
  {:else}
    <p class="error-message">Something went wrong.</p>
  {/if}
</div>

<style>
  .error-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 80vh;
    color: var(--gray-950);
    padding: var(--padding-xl);
    animation: fadeIn 1s ease-out forwards;
  }

  .status-code {
    font-size: clamp(4rem, 20vw, 8rem);
    color: var(--gray-400);
  }

  .error-message {
    font-size: var(--font-md);
    color: var(--gray-700);
  }
</style>
