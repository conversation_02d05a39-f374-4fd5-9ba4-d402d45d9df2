import type { RequestHand<PERSON> } from "@sveltejs/kit";
import { error as svelteKitError, json } from "@sveltejs/kit";
import { getBalenaAuthToken } from "$lib/server/getBalenaAuthToken.ts";
import { getBalenaUrl } from "$lib/helpers/get-dynamic-url.ts";

type Response = {
  tokenizationKey: string;
  directKey: string;
};

export const GET: RequestHandler = async ({ fetch, params }) => {
  if (!params.deviceId) {
    svelteKitError(400, "Missing deviceId");
  }

  const BALENA_LINK = `${getBalenaUrl(params.deviceId)}/payment/v1/config/gateway`;

  try {
    const accessToken = await getBalenaAuthToken(fetch);
    const response = await fetch(BALENA_LINK, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      svelteKitError(response.status, "Gateway request failed");
    }

    const data: Response = await response.json();

    return json(data.directKey);
  } catch (error) {
    console.error(error);
    svelteKitError(500, "Failed to get NMI key");
  }
};
