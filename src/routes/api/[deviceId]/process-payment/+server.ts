import type { Request<PERSON><PERSON><PERSON> } from "@sveltejs/kit";
import { error, json } from "@sveltejs/kit";
import type { processPayment } from "$lib/api/process-payment";
import { processNmiPayment } from "$lib/api/nmi";
import { logger } from "$lib/logger/logger.svelte.ts";
import {
  OrderState,
  OrderStateTracker,
} from "$lib/services/OrderStateTracker.server.ts";
import { submitOrder } from "$lib/api/submit-order";
import { emailReceipt } from "$lib/api/email-receipt.server.js";
import { getBridgeClient } from "$lib/client/bridge/bridge-client";

const bridgeClient = getBridgeClient();

export const POST: RequestHandler = async ({
  request,
  fetch,
  params,
  locals,
}) => {
  try {
    if (!params.deviceId) {
      error(400, "Missing device id");
    }

    const { nmiToken, orderInfo } = (await request.json()) as Parameters<
      typeof processPayment
    >[number];

    const { total } = await bridgeClient.getTotals({
      items: orderInfo.cart.items,
      subTotal: orderInfo.cart.subTotal,
      promisedTime: orderInfo.promisedTime.value,
    });

    orderInfo.cart.total = total;

    const parsedResult = await processNmiPayment({
      nmiToken,
      orderInfo,
      fetch,
      deviceId: params.deviceId,
    });

    await submitOrder({
      fetch,
      orderInfo,
      nmiDetails: parsedResult,
      deviceId: params.deviceId,
      bridgeApiToken: locals.bridgeApiToken || "",
    });

    const orderStateTracker = await OrderStateTracker.instance(orderInfo);
    await orderStateTracker.updateState(OrderState.COMPLETED);

    const receiptEmailInfo = structuredClone({
      ...orderInfo,
      ...parsedResult,
    });

    const { success: emailSent } = await emailReceipt(receiptEmailInfo);

    return json({
      paymentSuccessful: true,
      paymentResult: parsedResult,
      emailSent,
    });
  } catch (error) {
    logger.error({ error }, "Error in process-payment");
    return json({
      paymentSuccessful: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
