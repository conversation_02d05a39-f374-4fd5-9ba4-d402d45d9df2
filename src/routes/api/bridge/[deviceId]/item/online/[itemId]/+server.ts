import { error, json, type RequestHandler } from "@sveltejs/kit";
import { getApiBaseUrl } from "$lib/helpers/get-dynamic-url.ts";
import { detailedItemCache } from "$lib/services/DetailedItemCache.server.ts";
import type { DetailedItem } from "$lib/types";

export const GET: RequestHandler = async (event) => {
  const { locals, params } = event;
  const { bridgeApiToken } = locals;
  const { itemId, deviceId } = params;

  if (!deviceId || !itemId) {
    error(404, { message: "Missing deviceId or itemId" });
  }

  const cachedItem = await detailedItemCache.get(deviceId, itemId);
  if (cachedItem) {
    return json(cachedItem);
  }

  const bridgeEndpointAddress = `${getApiBaseUrl(deviceId)}/item/online/${itemId}`;

  const response = await fetch(bridgeEndpointAddress, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${bridgeApiToken}`,
    },
  });

  if (!response.ok) {
    // Handle non-OK responses properly
    return json(
      {
        error: `Failed to fetch item: ${response.status} ${response.statusText}`,
        itemId: params.itemId,
      },
      { status: response.status },
    );
  }

  const detailedItem: DetailedItem = await response.json();

  await detailedItemCache.set(deviceId, itemId, detailedItem);

  return json(detailedItem);
};
