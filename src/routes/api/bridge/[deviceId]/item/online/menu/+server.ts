import { error, json, type RequestHandler } from "@sveltejs/kit";
import { getApiBaseUrl } from "$lib/helpers/get-dynamic-url.ts";
import { logger } from "$lib/logger/logger.svelte.js";

export const GET: RequestHandler = async ({ params, locals }) => {
  const { bridgeApiToken } = locals;
  const { deviceId } = params;

  const bridgeEndpointAddress = `${getApiBaseUrl(deviceId)}/item/online/menu`;

  const response = await fetch(bridgeEndpointAddress, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${bridgeApiToken}`,
    },
  });

  if (!response.ok) {
    logger.error("Failed to get menu: ", {
      bridgeEndpointAddress,
      deviceId,
      response,
    });
    // Handle non-OK responses properly
    error(response.status, "Failed to get menu");
  }

  const body = await response.json();

  return json(body);
};
