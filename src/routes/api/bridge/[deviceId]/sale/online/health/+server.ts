import { getApiBaseUrl } from "$lib/helpers/get-dynamic-url.ts";
import { logger } from "$lib/logger/logger.svelte.js";
import { error, json, type RequestHandler } from "@sveltejs/kit";

export const GET: RequestHandler = async ({ params, locals }) => {
  const { bridgeApiToken } = locals;
  const { deviceId } = params;

  const bridgeEndpointAddress = `${getApiBaseUrl(deviceId)}/sale/online/health`;

  const response = await fetch(bridgeEndpointAddress, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${bridgeApiToken}`,
    },
  });

  if (!response.ok) {
    logger.error("Failed to get health");
    error(response.status, "Failed to get health");
  }

  const body = await response.json();

  return json(body);
};
