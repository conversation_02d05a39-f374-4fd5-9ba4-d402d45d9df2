import { json, type RequestHand<PERSON> } from "@sveltejs/kit";
import { getApiBaseUrl } from "$lib/helpers/get-dynamic-url.ts";
import type { DetailedItem, Totals } from "$lib/types";
import { logger } from "$lib/logger/logger.svelte.ts";
import { CheckoutError } from "$lib/services/errors/checkout/CheckoutError.ts";

export const POST: RequestHandler = async ({ request, locals, params }) => {
  const { bridgeApiToken } = locals;
  const { deviceId } = params;
  const requestData: {
    items: DetailedItem[];
    subTotal: number;
    promisedTime: number;
  } = await request.json();

  const response = await fetch(
    `${getApiBaseUrl(deviceId)}/sale/online/totals`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${bridgeApiToken}`,
      },
      body: JSON.stringify({
        items: requestData.items,
        promisedTime: requestData.promisedTime,
      }),
    },
  );

  const responseData: Totals = await response.json();

  logger.info(responseData, "Totals received from bridge");

  if (!response.ok) {
    logger.error(responseData, "Failed to get totals from bridge");
    throw new Error("Failed to get totals from bridge");
  }

  if (responseData.subTotal !== requestData.subTotal) {
    logger.error(
      {
        sent: requestData.subTotal,
        received: responseData.subTotal,
      },
      "Totals do not match",
    );
    throw new CheckoutError({
      name: "TOTALS_DO_NOT_MATCH",
      message: "Totals do not match",
      cause:
        "sent: " +
        requestData.subTotal +
        " and received: " +
        responseData.subTotal,
    });
  } else if (responseData.blockedReason.length > 0) {
    throw new CheckoutError({
      name: "SALE_BLOCKED",
      message: "Bridge returned a blocked reason",
      cause: responseData.blockedReason,
    });
  }

  return json(responseData);
};
