.tip-section-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  width: 100%;
}

.tip-grouper {
  display: flex;
  flex-wrap: wrap;
}

.tip-grouper input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.tip-selection {
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  border: 1px solid var(--gray-300);
  background-color: white;
  padding: var(--padding-sm) var(--padding-md);
  color: var(--text-color);
}

.tip-selection:first-of-type {
  border-top-left-radius: var(--radius-md);
  border-bottom-left-radius: var(--radius-md);
}

.tip-selection:last-of-type {
  border-top-right-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
}

.tip-selection:hover {
  border-color: var(--primary-color);
}

input[type="radio"]:checked + .tip-selection {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
  color: white;
}

@media (max-width: 460px) {
  .tip-selection {
    padding: var(--padding-sm);
    font-size: var(--font-sm);
  }
}

.tip-amount {
  color: var(--green-500);
}

.back-button {
  margin-bottom: var(--margin-md);
  border-radius: var(--radius-md);
  background-color: transparent;
  width: 200px;
  height: 50px;
}

.back-button-content {
  display: flex;
  align-items: center;
  gap: var(--padding-sm);
}

.extra-fees {
  display: flex;
  align-items: center;
}

.info-icon {
  margin-left: var(--margin-sm);
  color: var(--gray-900);
  font-size: var(--font-xs);
}

.checkout-page {
  margin: 0 auto;
  background-color: var(--gray-50);
  padding: var(--padding-lg);
  max-width: 700px;
}

h1 {
  margin-bottom: 2rem;
}

h2 {
  margin-bottom: 1rem;
  font-weight: 600;
  font-size: var(--font-lg);
}

h3 {
  margin-bottom: 1rem;
  font-weight: 600;
  font-size: var(--font-md);
}

section {
  margin-bottom: 1.5rem;
  box-shadow: var(--shadow-elevation-medium);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  background-color: white;
  padding: var(--padding-lg);
}

.pickup-info {
  display: grid;
  grid-template-columns: 2fr 2fr;
  align-items: start;
  gap: var(--padding-md);
  margin-block-end: var(--margin-md);
}

@media (max-width: 768px) {
  .pickup-info {
    grid-template-columns: 1fr;
  }
}

.personal-info {
  display: flex;
  flex-direction: column;
  margin-bottom: var(--margin-sm);
}

/** TODO: Add payment options when ready */

/* .payment-options {
  display: flex;
  flex-direction: column;
  gap: var(--padding-md);
}

.payment-option {
  display: flex;
  align-items: center;
  justify-items: stretch;
  padding: var(--padding-md);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  transition: all 0.2s ease-in-out;
}

.payment-option:hover {
  border-color: var(--primary-color);
}

.payment-option input[type="radio"] {
  margin-right: 1rem;
}

.card-icon,
.apple-icon,
.gpay-icon {
  color: var(--gray-500);
  margin-left: auto;
}

.payment-option input[type="radio"] {
  width: auto;
}

*/

.card-form {
  display: flex;
  flex-direction: column;
  gap: var(--padding-sm);
}

.personal-info label {
  margin-top: var(--padding-sm);
}

.personal-info input {
  margin-bottom: var(--padding-sm);
}

.card-details {
  display: flex;
  gap: 1rem;
}

.order-overview {
  display: flex;
  flex-direction: column;
  gap: var(--padding-md);
}

.amount {
  display: flex;
  justify-content: space-between;
}

.input-group-wrapper {
  display: flex;
  gap: 1rem;
}

.input-group {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.icon-cc-wrapper {
  max-width: 295px;
}

.input-wrapper {
  display: flex;
  width: 100%;
  min-height: 50px;
}

#ccNumber {
  flex-grow: 1;
  width: 100%;
}

.icon {
  display: flex;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  border: 1px solid var(--gray-300);
  border-right: none;
  border-top-left-radius: var(--radius-md);
  border-bottom-left-radius: var(--radius-md);
  padding: var(--padding-sm);
}

.form-skeleton-wrapper {
  position: relative;
}

.required-indicator {
  color: red;
}

.main-skeleton {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  transition: opacity 0.2s ease-out;
  box-shadow: var(--shadow-elevation-medium);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  background-color: white;
  padding: var(--padding-md);
  height: 100%;
}

.main-skeleton.hidden {
  opacity: 0;
  pointer-events: none;
}

.skeleton-title {
  animation: shine 1.5s linear infinite;
  margin-bottom: var(--margin-md);
  border-radius: var(--radius-sm);
  background: linear-gradient(
    to right,
    var(--gray-200) 8%,
    var(--gray-300) 18%,
    var(--gray-200) 33%
  );
  background-size: 800px 104px;
  width: 40%;
  height: 1rem;
}

.skeleton-input {
  animation: shine 1.5s linear infinite;
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
  background: linear-gradient(
    to right,
    var(--gray-200) 8%,
    var(--gray-300) 18%,
    var(--gray-200) 33%
  );
  background-size: 800px 104px;
  width: 100%;
  height: 40px;
}

.skeleton-group {
  display: flex;
  gap: var(--gap-xs);
  margin-bottom: var(--margin-md);
}

.small-skeleton {
  display: flex;
  flex: 1;
  flex-direction: column;
}

.skeleton-icon {
  flex-shrink: 0;
  animation: shine 1.5s linear infinite;
  border-radius: var(--radius-md) 0 0 var(--radius-md);
  background: linear-gradient(
    to right,
    var(--gray-200) 8%,
    var(--gray-300) 18%,
    var(--gray-200) 33%
  );
  background-size: 800px 104px;
  width: 40px;
  height: 40px;
}

.skeleton-label {
  animation: shine 1.5s linear infinite;
  margin-bottom: var(--margin-sm);
  border-radius: var(--radius-sm);
  background: linear-gradient(
    to right,
    var(--gray-200) 8%,
    var(--gray-300) 18%,
    var(--gray-200) 33%
  );
  background-size: 800px 104px;
  width: 30%;
  height: 1rem;
}

@keyframes shine {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

.extra-fees-content {
  display: flex;
  flex-direction: column;
  gap: var(--padding-md);
}

.extra-fees-amount {
  font-weight: 600;
  font-size: var(--font-lg);
}

.extra-fees-description {
  margin-top: var(--margin-sm);
  color: var(--gray-600);
}

.pickup-time {
  color: var(--gray-800);
  font-size: var(--font-md);
}

.amount-placeholder {
  display: flex;
  justify-content: space-between;
  opacity: 0.6;
}

.calculating {
  color: var(--gray-800);
  font-style: italic;
}

.warning-section {
  display: flex;
  align-items: flex-start;
  gap: var(--padding-md);
}

.warning-icon {
  margin-top: 2px;
  color: var(--blue-600);
  font-size: var(--font-lg);
}
