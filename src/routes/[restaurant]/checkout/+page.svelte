<script lang="ts">
  import { goto } from "$app/navigation";
  import { page } from "$app/stores";
  import { env } from "$env/dynamic/public";
  import { getBridgeClient } from "$lib/client/bridge/bridge-client.ts";
  import { client } from "$lib/client/query-client.svelte";
  import Button from "$lib/components/Button.svelte";
  import CartItems from "$lib/components/CartItems.svelte";
  import Drawer from "$lib/components/Drawer.svelte";
  import { initPickupTime } from "$lib/components/PickupTime.svelte";
  import Tooltip from "$lib/components/Tooltip.svelte";
  import { calcTip } from "$lib/helpers/calculate-tip.svelte";
  import {
    fieldValidationStates,
    initializeCollectJS,
  } from "$lib/helpers/initialize-collectJS";
  import {
    centToDollar,
    formatPhoneNumber,
    sanitizeCustomTipInput,
    validateTwoDecimalInput,
  } from "$lib/helpers/number.ts";
  import { retry } from "$lib/helpers/promises";
  import { routes } from "$lib/helpers/routes.js";
  import { validateCheckoutFields } from "$lib/helpers/validate-checkout-fields";
  import { getCartState } from "$lib/services/Cart.svelte";
  import { ErrorHandler } from "$lib/services/errors/checkout/ErrorHandler";
  import { getStoreInfoState } from "$lib/services/StoreInfo.svelte";
  import { toast } from "$lib/services/ToastManager.svelte";
  import type { DetailedItem, PaymentMethod } from "$lib/types";
  import { onMount } from "svelte";
  import { quintOut } from "svelte/easing";
  import { slide } from "svelte/transition";
  import LeftArrowIcon from "~icons/mdi/arrow-left";
  import CalendarIcon from "~icons/mdi/calendar-month";
  import CreditCardIcon from "~icons/mdi/credit-card";
  import InfoIcon from "~icons/mdi/information-outline";

  const { data } = $props();

  let continueChecked = $state(false);
  let customerFirstName = $state("");
  let customerLastName = $state("");
  let customerEmail = $state("");
  let customTipInput = $state("");
  let selectedTip: "none" | "15%" | "20%" | "25%" | string = $state("none");
  let selectedPaymentMethod: PaymentMethod = $state("credit-card");
  let paymentToken: string;
  let cardLastFour: string;
  let isLoading = $state(false);
  let customerPhone = $state("");
  let pickupNote = $state("");
  let tax = $state(0);
  let isCollectJSLoading = $state(true);
  let extraFees = $state(0);
  let ExtraFeesDrawer: Drawer;

  const bridgeClient = getBridgeClient();
  const cart = getCartState();
  const storeInfo = getStoreInfoState();
  const merchantAddress = storeInfo.formattedAddress;
  const merchantName = storeInfo.name;
  const currentTip = $derived(
    calcTip(selectedTip, cart.subTotal, customTipInput),
  );
  const checkoutTotal = $derived(cart.subTotal + currentTip + tax + extraFees);
  const displayTotal = $derived(centToDollar(checkoutTotal)); // for animations

  const updateTotals = async ({
    items,
    subTotal,
    promisedTime,
  }: {
    items: DetailedItem[];
    subTotal: number;
    promisedTime: number;
  }) => {
    try {
      const { taxTotal, takeOutSurchargeTotal } = await retry(
        () =>
          bridgeClient.getTotals({
            items,
            promisedTime,
            subTotal,
          }),
        {
          retries: 10,
          retryIntervalMs: 500,
        },
      );
      tax = taxTotal;
      extraFees = takeOutSurchargeTotal;
    } catch (error) {
      console.error("Error getting tax totals:", error);
    }
  };

  $effect(() => {
    updateTotals({
      items: cart.checkoutItems,
      subTotal: cart.subTotal,
      promisedTime: initPickupTime().value,
    });
  });

  $effect(() => {
    if (customerPhone) {
      const formatted = formatPhoneNumber(customerPhone);
      if (formatted !== customerPhone) {
        customerPhone = formatted;
      }
    }
  });

  const initializeToken = async () => {
    return new Promise<string>((resolve) => {
      const checkToken = () => {
        if (paymentToken) {
          resolve(paymentToken);
        } else {
          setTimeout(checkToken, 100); // Check every 100ms
        }
      };
      checkToken();
    });
  };

  const handleCheckout = async () => {
    isLoading = true;

    try {
      validateCheckoutFields(
        customerFirstName,
        customerLastName,
        customerEmail,
        customerPhone,
      );

      await retry(() => bridgeClient.getHealth(), {
        retries: 10,
        retryIntervalMs: 300,
      });

      window.CollectJS.startPaymentRequest();

      const token = await initializeToken();

      const pickupTime = initPickupTime();

      const orderInfo = {
        memo: pickupNote,
        /**
         * TODO This is a temporary way to hardcode the pickup time.
         * We need to find a better way to handle this in the future.
         */
        promisedTime: pickupTime,
        paymentMethod: selectedPaymentMethod,
        storeInfo: storeInfo.info,
        cart: {
          items: cart.checkoutItems,
          total: checkoutTotal,
          tip: currentTip,
          tax,
          subTotal: cart.subTotal,
          totalItemsCount: cart.checkoutItems.length,
        },
        customer: {
          firstName: customerFirstName,
          lastName: customerLastName,
          phone: customerPhone,
          email: customerEmail,
        },
        card: {
          pan: cardLastFour,
        },
      };

      const { paymentSuccessful, paymentResult, emailSent } =
        await client.processPayment({
          nmiToken: token,
          deviceId: data.restaurantInfo.device_id,
          orderInfo,
        });

      if (paymentSuccessful) {
        cart.clear();

        await goto(
          routes.thankYou({
            transactionId: paymentResult.transactionid,
            restaurant: $page.params.restaurant ?? "",
            emailSent,
          }),
        );
      } else {
        toast.error(
          "Payment failed. Please try again later or contact support.",
        );
      }
    } catch (error) {
      ErrorHandler.sendUserFriendlyError(error);
    } finally {
      isLoading = false;
    }
  };

  const selectTip = (tip: string) => {
    if (tip === "custom") {
      const tipAmount = Number(customTipInput);
      const tipPercentage = (tipAmount / (cart.subTotal / 100)) * 100;

      if (tipPercentage >= 50) {
        if (
          confirm(`Are you sure you want to tip ${tipPercentage.toFixed(2)}%?`)
        ) {
          selectedTip = tip;
        } else {
          customTipInput = "";
          return;
        }
      } else {
        selectedTip = tip;
      }
    } else {
      selectedTip = tip;
    }
  };

  const handleContinue = async () => {
    if (
      fieldValidationStates.ccnumber &&
      fieldValidationStates.ccexp &&
      fieldValidationStates.cvv
    ) {
      continueChecked = true;
      setTimeout(() => {
        // animations take time to complete
        const customTipInput = document.getElementById("first-name");
        if (customTipInput) {
          customTipInput.focus();
        }
        window.scrollTo(0, document.body.scrollHeight);
      }, 200);
    } else {
      toast.error("Please check your card details and try again.");
    }
  };

  const vocalizeTipForSR = () => {
    const liveRegion = document.getElementById("tip-live-region");
    if (!liveRegion) return;

    liveRegion.textContent = `Tip updated to ${centToDollar(currentTip)}. This amounts to a total of ${displayTotal}`;
  };

  const mutationObserver = (node: HTMLElement) => {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (
          mutation.type === "attributes" &&
          mutation.target instanceof HTMLElement &&
          // CCExp is the last iFrame to load
          mutation.target.id === "CollectJSInlineccexp"
        ) {
          isCollectJSLoading = false;
          observer.disconnect();
        } else {
          console.warn("CollectJSInlineccexp not found: ", mutation);
        }
      });
    });

    observer.observe(node, {
      attributes: true,
      subtree: true,
    });

    return {
      destroy() {
        observer.disconnect();
      },
    };
  };

  onMount(async () => {
    const MAX_RETRIES = 3;
    let retryCount = 0;

    const tryInitialize = async () => {
      try {
        const result = await initializeCollectJS({
          tokenizationKey: env.PUBLIC_NMI_PUBLIC_KEY ?? "",
          cartTotal: checkoutTotal,
        });

        if (result) {
          paymentToken = result.token;
          cardLastFour = result.cardLastFour;
        }
        return true;
      } catch (error) {
        console.error("Error initializing CollectJS:", error);
        return false;
      }
    };

    while (retryCount < MAX_RETRIES) {
      const success = await tryInitialize();
      if (success) break;
      retryCount++;
      // Exponential backoff
      await new Promise((r) => setTimeout(r, 1000 * Math.pow(2, retryCount)));
    }
  });
</script>

{#snippet requiredLabel()}
  <span class="required-indicator" aria-hidden="true"> * </span>
{/snippet}

<div class="checkout-page">
  <button class="back-button" onclick={() => window.history.back()}>
    <span class="back-button-content">
      <LeftArrowIcon aria-hidden="true" /> Back to menu
    </span>
  </button>
  <h1>Checkout</h1>

  <section>
    <h2>Pickup Details</h2>
    <div class="pickup-info">
      <div>
        <h3>Pickup from:</h3>
        <p>{merchantName}</p>
        <p>{merchantAddress}</p>
      </div>
      <div>
        <h3>Pickup Time:</h3>
        <p class="pickup-time">
          About 30-45 minutes after your order is placed.
        </p>
      </div>
    </div>
  </section>

  <!-- TODO: Add Apple Pay and Google Pay payment methods -->
  <!-- <div class="section">
    <h2>Payment Method</h2>
    <div class="payment-options">
      <label class="payment-option">
        <input
          type="radio"
          name="payment"
          value="credit-card"
          aria-label="Pay with credit card"
          bind:group={selectedPaymentMethod}
        />
        <span>Credit Card</span>
        <span class="card-icon"><CreditCard /></span>
      </label>
      <label class="payment-option">
        <input
          type="radio"
          name="payment"
          value="google-pay"
          aria-label="Pay with Google Pay"
          bind:group={selectedPaymentMethod}
          onclick={() => {
            continueChecked = false;
          }}
        />
        <span>Google Pay</span>
        <span class="gpay-icon"><GoogleLogo /></span>
      </label>
      <label class="payment-option">
        <input
          type="radio"
          name="payment"
          value="apple-pay"
          aria-label="Pay with Apple Pay"
          bind:group={selectedPaymentMethod}
          onclick={() => (continueChecked = false)}
        />
        <span>Apple Pay</span>
        <span class="apple-icon"><AppleLogo /></span>
      </label>
    </div>
  </div> -->

  {#snippet inputGroupSkeleton()}
    <div class="skeleton-label"></div>
    <div class="skeleton-group">
      <div class="skeleton-icon"></div>
      <div class="skeleton-input"></div>
    </div>
  {/snippet}

  <div class="form-skeleton-wrapper">
    <div class="main-skeleton" class:hidden={!isCollectJSLoading}>
      <div class="skeleton-title"></div>
      {@render inputGroupSkeleton()}
      <div class="skeleton-group">
        <div class="small-skeleton">
          {@render inputGroupSkeleton()}
        </div>
        <div class="small-skeleton">
          {@render inputGroupSkeleton()}
        </div>
      </div>
    </div>

    <section
      use:mutationObserver
      class:visible={selectedPaymentMethod === "credit-card"}
    >
      <h3>Card Details</h3>
      <div inert={false}>
        <form id="collectJS-form" class="card-form">
          <!-- svelte-ignore a11y_no_noninteractive_tabindex -->
          <!-- We are forced to make this tabbable because ccNumber is an input field-->
          <label
            for="ccnumber"
            aria-label="Card number required."
            tabindex={0}
            inert={selectedPaymentMethod !== "credit-card"}
          >
            Card number
            {@render requiredLabel()}
            <div class="input-wrapper">
              <div class="icon">
                <CreditCardIcon />
              </div>
              <div id="ccNumber"></div>
            </div>
          </label>
          <div class="card-details">
            <div class="icon-cc-wrapper">
              <!-- svelte-ignore a11y_no_noninteractive_tabindex -->
              <!-- We are forced to make this tabbable because ccExp is an input field-->
              <label
                for="ccexp"
                aria-label="Expiration date required."
                tabindex={0}
                inert={selectedPaymentMethod !== "credit-card"}
              >
                Expiry date
                {@render requiredLabel()}
                <div class="input-wrapper">
                  <div class="icon">
                    <CalendarIcon />
                  </div>
                  <div id="ccExp"></div>
                </div>
              </label>
            </div>
            <!-- svelte-ignore a11y_no_noninteractive_tabindex -->
            <!-- We are forced to make this tabbable because ccCVV is an input field-->
            <label
              for="cvv"
              aria-label="CVV required."
              tabindex={0}
              inert={selectedPaymentMethod !== "credit-card"}
            >
              CVV/CVC
              {@render requiredLabel()}
              <div id="ccCVV"></div>
            </label>
          </div>
        </form>
      </div>
    </section>
  </div>

  <section>
    <h2>Your Order</h2>
    <div class="order-overview">
      <CartItems />

      <span>Tip:</span>
      <div class="tip-section-wrapper">
        <div class="tip-grouper">
          <input
            type="radio"
            name="tip"
            id="tip-none"
            value="none"
            aria-label="No tip"
            checked={selectedTip === "none"}
            onchange={() => {
              selectTip("none");
              vocalizeTipForSR();
            }}
          />
          <label class="tip-selection" for="tip-none">None</label>

          <input
            type="radio"
            name="tip"
            id="tip-15"
            value="15%"
            aria-label="15% tip"
            checked={selectedTip === "15%"}
            onchange={() => {
              selectTip("15%");
              vocalizeTipForSR();
            }}
          />
          <label class="tip-selection" for="tip-15">15%</label>

          <input
            type="radio"
            name="tip"
            id="tip-20"
            value="20%"
            aria-label="20% tip"
            checked={selectedTip === "20%"}
            onchange={() => {
              selectTip("20%");
              vocalizeTipForSR();
            }}
          />
          <label class="tip-selection" for="tip-20">20%</label>

          <input
            type="radio"
            name="tip"
            id="tip-25"
            value="25%"
            aria-label="25% tip"
            checked={selectedTip === "25%"}
            onchange={() => {
              selectTip("25%");
              vocalizeTipForSR();
            }}
          />
          <label class="tip-selection" for="tip-25">25%</label>

          <input
            type="radio"
            name="tip"
            id="tip-custom"
            value="custom"
            aria-label="Custom tip"
            checked={selectedTip === "custom"}
            onchange={() => {
              selectTip("custom");
              setTimeout(() => {
                const customTipInput = document.getElementById("custom-tip");
                if (customTipInput) {
                  customTipInput.focus();
                }
              }, 300);
            }}
          />
          <label class="tip-selection" for="tip-custom">Custom</label>
        </div>
      </div>

      <div id="tip-live-region" class="sr-only" aria-live="polite"></div>
      <div class="amount">
        <span>Item Subtotal:</span>
        <span>{centToDollar(cart.subTotal)}</span>
      </div>
      {#if tax}
        <div class="amount">
          <span>Sales Tax:</span>
          <span>+ {centToDollar(tax)}</span>
        </div>
      {:else}
        <div class="amount-placeholder">
          <span>Sales Tax:</span>
          <span class="calculating">Calculating...</span>
        </div>
      {/if}
      {#if extraFees}
        <div class="amount">
          <div class="extra-fees">
            <span>Additional Fees:</span>
            <Tooltip
              content="Extra fees applied to takeout orders."
              position="top"
              maxWidth={180}
            >
              <InfoIcon class="info-icon" />
            </Tooltip>
            <InfoIcon
              aria-label="Additional fees information"
              onclick={() => {
                ExtraFeesDrawer.open();
              }}
              class="info-icon mobile-view"
            />
          </div>
          <span>+ {centToDollar(extraFees)}</span>
        </div>
      {/if}
      {#if selectedTip !== "none"}
        <div
          class="amount"
          transition:slide={{ duration: 300, easing: quintOut }}
        >
          <span>Tip amount:</span>
          <span class="tip-amount">+ {centToDollar(currentTip)}</span>
        </div>
      {/if}
      {#if selectedTip === "custom"}
        <div transition:slide={{ duration: 300, easing: quintOut }}>
          <label for="custom-tip">Custom tip:</label>
          <input
            bind:value={
              () => {
                return customTipInput;
              },
              (val) => {
                customTipInput = sanitizeCustomTipInput(val);
              }
            }
            id="custom-tip"
            type="text"
            inputmode="decimal"
            placeholder="Enter custom tip in dollars"
            onblur={() => selectTip("custom")}
            oninput={(e) => {
              validateTwoDecimalInput(e);
              vocalizeTipForSR();
            }}
          />
        </div>
      {/if}
      <div class="amount">
        <h3>Total:</h3>
        {#key displayTotal}
          <h3 in:slide={{ duration: 500, easing: quintOut }}>
            {displayTotal}
          </h3>
        {/key}
      </div>
    </div>
  </section>

  {#if selectedPaymentMethod !== "credit-card"}
    <button class="button--primary-lg" onclick={handleCheckout}
      >Complete Order
    </button>
  {/if}

  {#if selectedPaymentMethod === "credit-card" && !continueChecked}
    <button class="button--primary-lg" onclick={handleContinue}>Continue</button
    >
  {/if}

  {#if continueChecked}
    <section>
      <span class="warning-section">
        <span class="warning-icon">
          <InfoIcon />
        </span>
        <h2>Allergies & Dietary Restrictions</h2>
      </span>

      <p>
        If you have allergies or dietary restrictions, please call us at:
        <a href="tel:{storeInfo.phone}">{storeInfo.phone}</a>
      </p>
    </section>

    <section transition:slide={{ duration: 300, easing: quintOut }}>
      <h2>Your Information</h2>
      <div class="personal-info">
        <div class="input-group-wrapper">
          <div class="input-group">
            <label for="name">First Name</label>
            <input
              bind:value={customerFirstName}
              id="name"
              type="text"
              placeholder="First Name"
              maxlength="100"
              required
            />
          </div>
          <div class="input-group">
            <label for="last-name">Last Name</label>
            <input
              bind:value={customerLastName}
              id="last-name"
              type="text"
              placeholder="Last Name"
              maxlength="100"
              required
            />
          </div>
        </div>
        <div class="input-group-wrapper">
          <div class="input-group">
            <label for="email">Email</label>
            <input
              bind:value={customerEmail}
              id="email"
              type="email"
              placeholder="Email"
              maxlength="100"
              required
            />
          </div>
          <div class="input-group">
            <label for="phone">Phone</label>
            <input
              bind:value={customerPhone}
              id="phone"
              type="tel"
              placeholder="Phone"
              required
            />
          </div>
        </div>
      </div>
      <Button title="Complete Order" onClick={handleCheckout} {isLoading} />
    </section>
  {/if}
</div>

<Drawer title="Additional Fees" bind:this={ExtraFeesDrawer}>
  <div class="extra-fees-content">
    <span class="extra-fees-amount">
      Additional fee of: {centToDollar(extraFees)}</span
    >
    <p class="extra-fees-description">
      Additional fees are applied to takeout orders. These fees are used to
      cover the cost of processing the order and all of the included takeout
      fees.
    </p>
  </div>
</Drawer>

<style>
  @import "./checkout.css";
</style>
