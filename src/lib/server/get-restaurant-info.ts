import { MockBridgeClient } from "$lib/client/bridge/bridge-client.mock.ts";
import {
  BridgeClient,
  type IBridgeClient,
} from "$lib/client/bridge/bridge-client.ts";
import { logger } from "$lib/logger/logger.svelte.ts";
import type { SvelteFetch } from "$lib/types";
import { error as svelteKitError } from "@sveltejs/kit";
import { z } from "zod";
import { privateConfig } from "../../privateConfig.server";
import { publicConfig } from "../../publicConfig";

let restaurantConfig: z.infer<typeof restaurantInfoSchema> | undefined;

const restaurantInfoSchema = z.record(
  /**
   * Restaurant unique name
   */
  z.string(),
  z.object({
    uuid: z.string(),
    device_id: z.string(),
    public_nmi_key: z.string(),
  }),
);

export const getRestaurantInfo = async (
  fetch: SvelteFetch,
  restaurant: string,
): Promise<
  { device_id: string } & Awaited<ReturnType<IBridgeClient["getMenu"]>>
> => {
  const BridgeClientClass =
    publicConfig.env === "dev" ? MockBridgeClient : BridgeClient;

  if (restaurantConfig) {
    const { device_id } = restaurantConfig[restaurant] ?? {};

    if (!device_id) {
      logger.error(`Missing device_id for restaurant: ${restaurant}`);
      svelteKitError(404, "Not found");
    }

    const menu = await new BridgeClientClass(fetch, device_id).getMenu();

    return {
      device_id,
      ...menu,
    };
  }

  try {
    const data = restaurantInfoSchema.parse(
      JSON.parse(privateConfig.restaurantConfig),
    );

    restaurantConfig = data;

    if (!data[restaurant]) {
      logger.error(`Missing restaurant: ${restaurant}`);
      svelteKitError(404, "Not found");
    }

    const { device_id } = data[restaurant];
    logger.trace({ restaurant: restaurant }, "Found device id for restaurant");

    const bridgeClient = new BridgeClientClass(fetch, device_id);
    const menu = await bridgeClient.getMenu();

    return { device_id, ...menu };
  } catch (error) {
    logger.error(error, "Error getting menu from bridge client");
    svelteKitError(500, "Failed to get restaurant info");
  }
};
