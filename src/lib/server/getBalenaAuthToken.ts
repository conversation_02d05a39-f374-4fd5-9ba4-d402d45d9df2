import { privateConfig } from "../../privateConfig.server.ts";

export const getBalenaAuthToken = async (
  fetch: typeof globalThis.fetch,
): Promise<string> => {
  const body = {
    client_id: privateConfig.balenaClientId,
    client_secret: privateConfig.balenaClientSecret,
    audience: privateConfig.balenaAudience,
    grant_type: privateConfig.balenaGrantType,
  };

  const response = await fetch(`${privateConfig.balenaAuthTokenUrl}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(body),
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch auth token: ${response.status}`);
  }

  const data = await response.json();
  return data.access_token as string;
};
