import type { <PERSON>velteFetch } from "$lib/types";

export const getNMIKey = async (fetch: SvelteFetch, deviceId: string) => {
  try {
    const response = await fetch(`/api/${deviceId}/NMI-key`, {
      method: "GET",
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch NMI key: ${response.status}`);
    }

    const directKey = await response.json();
    return directKey as string;
  } catch (error) {
    console.error("Error fetching NMI key:", error);
    throw error;
  }
};
