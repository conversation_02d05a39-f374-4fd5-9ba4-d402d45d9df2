import { logger } from "$lib/logger/logger.svelte";
import { redis } from "$lib/server/redis.server.ts";
import type { RequestEvent } from "@sveltejs/kit";

export type SessionKeyPrefix = "session" | "ip";

export type Session = {
  requests: { [routeId: string]: number; totalRequests: number };
};

export const getStorageKey = (prefix: SessionKeyPrefix, id: string) => {
  return `${prefix}:${id}`;
};

export const createIpSession = async (
  event: RequestEvent,
  ageInSeconds: number,
) => {
  const ip = event.request.headers.get("X-Real-Ip");
  if (!ip) {
    logger.error({ ip, event }, "IP not found");
    throw new Error("IP not found");
  }
  const emptySession: Session = { requests: { totalRequests: 0 } };

  await redis.setex(
    getStorageKey("ip", ip),
    ageInSeconds,
    JSON.stringify(emptySession),
  );

  return { ip };
};

export const createBrowserSession = async (
  event: RequestEvent,
  ageInSeconds: number,
) => {
  const { cookies } = event;
  const sessionId = crypto.randomUUID();
  const emptySession: Session = { requests: { totalRequests: 0 } };

  cookies.set("session_id", sessionId, {
    path: "/",
    httpOnly: true,
    sameSite: "strict", // TODO Will this cause any issues with checkout?
    secure: true, // TODO Will this break dev?
    maxAge: ageInSeconds * 1000,
  });

  await redis.setex(
    getStorageKey("session", sessionId),
    ageInSeconds,
    JSON.stringify(emptySession),
  );

  return { sessionId };
};
