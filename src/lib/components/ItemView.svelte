<script lang="ts">
  import { shouldRenderChildrenAsLabels } from "$lib/components/Modifier/helpers.ts";
  import { calculateTotalItemPrice } from "$lib/helpers/calculate-total-item-price";
  import { hasModifiers, hasSomeSelectedChildren } from "$lib/helpers/item.ts";
  import { centToDollar } from "$lib/helpers/number.ts";
  import { getCartState } from "$lib/services/Cart.svelte";
  import { initStackedSnippetState } from "$lib/services/StackedSnippetState.svelte.js";
  import type { DetailedItem } from "$lib/types";
  import { onMount } from "svelte";
  import {
    ModifierListState,
    setModifierListState,
  } from "./Modifier/modifier-list-state.svelte";
  import ModifierList from "./Modifier/ModifierList.svelte";
  import QuantitySelector from "./QuantitySelector.svelte";
  import Stack from "./Stack.svelte";

  const MINIMUM_QUANTITY = 1;

  let {
    detailedItem,
    modifierListState,
    onAddToCart,
    editing = false,
    initialQuantity = MINIMUM_QUANTITY,
    isOnline = true,
    onDetailedItemLoad,
  }: {
    detailedItem: DetailedItem;
    modifierListState?: ModifierListState;
    onAddToCart: (params: { item: DetailedItem }) => void;
    editing?: boolean;
    initialQuantity?: number;
    isOnline?: boolean;
    onDetailedItemLoad?: (detailedItem: DetailedItem) => void;
  } = $props();

  let quantity: number = $state(Math.max(initialQuantity, MINIMUM_QUANTITY));
  let modifierList: ReturnType<typeof ModifierList> | undefined =
    $state(undefined);
  modifierListState = setModifierListState(modifierListState);

  const stackedSnippetState = initStackedSnippetState();

  const cart = getCartState();
  const total = $derived(calculateTotalItemPrice(detailedItem));
  const isItemWithDynamicPrice = $derived(
    hasModifiers(detailedItem) &&
      detailedItem.price === 0 &&
      hasModifiers(detailedItem),
  );
  const hasIncompleteRequiredSelections = $derived(
    detailedItem.modifiers.some(
      (child) =>
        child.selected &&
        child.modifiers.some(
          (grandChild) => grandChild.modMinSel > 0 && !grandChild.selected,
        ),
    ) ?? false,
  );

  const disableAddToCartButton = $derived.by(() => {
    if (!isOnline) {
      return true;
    }

    if (modifierListState.someModifiersAreInvalid) {
      return true;
    }

    if (
      isItemWithDynamicPrice &&
      (!modifierListState.allModifiersAreValid ||
        !hasSomeSelectedChildren(detailedItem) ||
        hasIncompleteRequiredSelections)
    ) {
      return true;
    }

    return false;
  });

  $effect(() => {
    detailedItem.qty = quantity;
  });

  onMount(async () => {
    onDetailedItemLoad?.(detailedItem);
  });

  const updateModifierSelection = (mods: DetailedItem[]) => {
    mods.forEach((mod) => {
      if (hasModifiers(mod)) {
        mod.selected = hasSomeSelectedChildren(mod);
      }
    });
  };

  const handleCartUpdate = () => {
    // If the item has selected children, set the item to selected
    if (hasModifiers(detailedItem)) {
      updateModifierSelection(detailedItem.modifiers);
    }

    if (!editing) {
      cart.add(detailedItem, modifierListState);
    }

    onAddToCart({
      item: detailedItem,
    });
  };
</script>

{#if "error" in detailedItem}
  <div class="error-container">
    <p class="error-message">
      There was an error loading this item. Please try again later.
    </p>
  </div>
{:else}
  <div class="quantity-selector-wrapper">
    <QuantitySelector
      max={detailedItem.count}
      bind:quantity
      price={detailedItem.price}
    />
  </div>

  {#if hasModifiers(detailedItem)}
    <ModifierList
      bind:this={modifierList}
      modifierList={detailedItem.modifiers}
      renderAsLabels={shouldRenderChildrenAsLabels(detailedItem, false)}
      isStacked={false}
    />
  {/if}

  <div class="add-to-cart-wrapper">
    <button
      disabled={disableAddToCartButton}
      onclick={handleCartUpdate}
      class="button--primary-lg"
    >
      <span>{editing ? "Update item" : "Add to cart"}</span>
      <span class="item-total">{centToDollar(total)}</span>
    </button>
    {#if !isOnline}
      <p class="offline-message">
        Restaurant is currently not accepting orders.
      </p>
    {/if}
  </div>

  <Stack {stackedSnippetState}></Stack>
{/if}

<style>
  .add-to-cart-wrapper {
    margin-top: auto;
    background-image: linear-gradient(0deg, white 80%, transparent);
    padding: var(--padding-md);
  }

  .quantity-selector-wrapper {
    padding-inline: var(--padding-md);
    padding-bottom: var(--padding-md);
  }

  .offline-message {
    margin-top: var(--padding-sm);
    color: var(--red-700);
    font-size: var(--font-sm);
    text-align: center;
  }

  button.button--primary-lg {
    display: flex;
    justify-content: space-between;
  }

  .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }

  .error-message {
    color: darkred;
    font-size: var(--font-sm);
  }
</style>
