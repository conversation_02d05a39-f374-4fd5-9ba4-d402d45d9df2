<script lang="ts">
  import { getCartState } from "$lib/services/Cart.svelte.ts";
  import { centToDollar } from "$lib/helpers/number.ts";
  import CartIcon from "~icons/mdi/cart";

  const cart = getCartState();

  let { onclick } = $props<{ onclick: () => void }>();
</script>

<div class="cart-container">
  <button
    class="cart-button button--primary-lg"
    aria-labelledby="cart-button-label"
    {onclick}
  >
    <div class="cart-icon-wrapper">
      <CartIcon />
      <span class="cart-item-count">{cart.totalItemsCount}</span>
      <span class="sr-only">item(s) in cart</span>
    </div>
    <span id="cart-button-label" class="cart-info">View Cart</span>
    <span class="cart-total">{centToDollar(cart.subTotal)}</span>
  </button>
</div>

<style>
  .cart-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .cart-button {
    position: fixed;
    bottom: var(--padding-lg);
    font-size: var(--font-sm);
    padding: var(--padding-sm);
    box-shadow: var(--shadow-elevation-high);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--gap-md);
    width: auto;

    @media (min-width: 640px) {
      padding: var(--padding-md);
      font-size: var(--font-md);
    }
  }

  .cart-icon-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .cart-icon-wrapper :global(svg) {
    width: var(--width-sm);
    height: var(--height-sm);
    fill: currentColor;
  }

  .cart-item-count {
    position: absolute;
    top: -5px;
    right: -10px;
    background-color: var(--gray-50);
    color: black;
    border-radius: 50%;
    width: 15px;
    height: 15px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .cart-info {
    flex-grow: 1;
    text-align: center;
  }

  .cart-total {
    opacity: 0.9;
  }

  @media (min-width: 1025px) {
    .cart-button {
      display: none;
    }
  }
</style>
