<script lang="ts">
  import SearchIcon from "~icons/mdi/magnify";
  import CloseIcon from "~icons/mdi/close";
  import { slide } from "svelte/transition";

  let { searchTerm = $bindable(), showSearch = $bindable() } = $props();

  let searchInput = $state<HTMLInputElement>();

  const toggleSearch = (event: MouseEvent) => {
    event.stopPropagation();
    showSearch = true;
    setTimeout(() => {
      searchInput?.focus();
    }, 100);
  };

  $effect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (
        showSearch &&
        searchInput &&
        !searchInput.contains(e.target as Node) &&
        searchTerm.trim() === ""
      ) {
        showSearch = false;
      }
    };

    if (showSearch) {
      window.addEventListener("click", handleClickOutside);
    }
    return () => {
      window.removeEventListener("click", handleClickOutside);
    };
  });
</script>

<div class="search-container">
  {#if !showSearch}
    <button
      onclick={toggleSearch}
      class="search-button"
      aria-label="Toggle search"
    >
      <SearchIcon font-size={15} />
    </button>
  {:else}
    <div class="input-wrapper" transition:slide={{ duration: 200, axis: "x" }}>
      <div class="input-field">
        <input
          class="search-input"
          type="text"
          placeholder="Search..."
          maxlength="100"
          bind:value={searchTerm}
          bind:this={searchInput}
        />
      </div>
      <button
        class="search-button"
        aria-label="Close search"
        onclick={() => {
          showSearch = false;
          searchTerm = "";
        }}
      >
        <CloseIcon />
      </button>
    </div>
  {/if}
</div>

<style>
  .search-container {
    display: flex;
    align-items: center;
  }

  .input-wrapper {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .search-button {
    display: flex;
    background: none;
    padding: var(--padding-sm);
    border-radius: var(--radius-sm);

    &:hover {
      background-color: var(--gray-200);
    }
  }

  .search-input {
    width: 100%;
    border: 1px solid var(--gray-300);
    background-color: white;
    padding: var(--padding-sm);
    border-radius: 0.5rem;
  }

  .search-input:focus {
    outline: none;
    border-color: var(--primary-color);
  }
</style>
