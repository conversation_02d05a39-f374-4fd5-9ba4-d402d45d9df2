<script lang="ts">
  import { formatMillisToMinutes } from "$lib/helpers/time";
  import WarningIcon from "~icons/mdi/alert";
  import ClockIcon from "~icons/mdi/clock-alert";

  type Props =
    | { type: "offline" }
    | { type: "closing-soon"; timeUntilClosingInMs: number };

  let props: Props = $props();
</script>

<header class="offline-banner" aria-live="polite">
  <div class="icon-container">
    {#if props.type === "offline"}
      <WarningIcon color="#fcba03" />
    {:else if props.type === "closing-soon"}
      <ClockIcon color="#fcba03" />
    {/if}
  </div>
  <div class="message">
    {#if props.type === "offline"}
      <p>Restaurant is currently not accepting orders</p>
      <p class="sub-message">
        You can browse the menu but cannot place orders at this time
      </p>
    {:else if props.type === "closing-soon"}
      <p>Restaurant is closing soon</p>
      <p class="sub-message">
        Online ordering will be unavailable in {formatMillisToMinutes(
          props.timeUntilClosingInMs,
        )}
        {props.timeUntilClosingInMs < 60000 ? "minute" : "minutes"}
      </p>
    {/if}
  </div>
</header>

<style>
  .offline-banner {
    display: flex;
    position: relative;
    align-items: center;
    gap: var(--gap-md);
    border-bottom: 1px solid #ffecb3;
    background-color: #fff8e1;
    padding: var(--padding-md);
    width: 100%;
  }

  .icon-container {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: var(--font-xl);
  }

  .message {
    font-weight: 500;
    font-size: var(--font-sm);
  }

  .sub-message {
    color: var(--gray-700);
    font-size: var(--font-sm);
  }
</style>
