<script lang="ts" module>
  export const openDepartmentDrawer = () => {
    departmentDrawer?.open();
  };

  let departmentDrawer: Drawer | undefined = $state(undefined);
</script>

<script lang="ts">
  import Drawer from "$lib/components/Drawer.svelte";
  import type { BasicDepartment } from "$lib/types";

  let { departments }: { departments: BasicDepartment[] } = $props();
</script>

<Drawer
  bind:this={departmentDrawer}
  title="Categories"
  onClose={() => departmentDrawer?.close()}
>
  <ul>
    {#each departments as department}
      {#if department}
        <li>
          <a
            href="#{department.title.toLowerCase().replace(/ /g, '-')}"
            onclick={() => departmentDrawer?.close()}
            class="department-drawer-link"
          >
            {department.title}
          </a>
        </li>
      {/if}
    {/each}
  </ul>
</Drawer>

<style>
  ul {
    list-style: none;
  }

  li:last-child {
    border-bottom: none;
  }

  .department-drawer-link {
    display: block;
    padding: var(--padding-md);
    background: none;
    text-decoration: none;
    color: black;
  }

  li {
    list-style: none;
  }
</style>
