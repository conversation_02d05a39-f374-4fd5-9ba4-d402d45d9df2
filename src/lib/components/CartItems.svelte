<script lang="ts">
  import { calculateTotalItemPrice } from "$lib/helpers/calculate-total-item-price";
  import { centToDollar } from "$lib/helpers/number.ts";
  import { getCartState } from "$lib/services/Cart.svelte.ts";
  import type { DetailedItem } from "$lib/types";
  import { flip } from "svelte/animate";
  import { fade } from "svelte/transition";
  import ItemView from "./ItemView.svelte";
  import Modal from "./Modal.svelte";
  import { ModifierListState } from "./Modifier/modifier-list-state.svelte";

  const cart = getCartState();

  let originalQuantity: number;
  let editItemModal: ReturnType<typeof Modal> | undefined = $state(undefined);
  let detailedItemClone: DetailedItem | null = $state(null);
</script>

{#snippet selectedModifiers({
  desc,
  modifierDesc,
  modifiers,
  selected,
}: DetailedItem)}
  {#if selected || modifiers?.some((mod) => mod.selected)}
    <li>
      <span>{modifierDesc || desc}</span>
      {#if modifiers?.length}
        <ul class="sublist">
          {#each modifiers as mod}
            {@render selectedModifiers(mod)}
          {/each}
        </ul>
      {/if}
    </li>
  {/if}
{/snippet}

<ul class="cart-items" aria-label="Cart Items">
  {#each cart.items as item, index (item.cartItemId)}
    <li
      class="cart-item"
      animate:flip={{ duration: 200 }}
      out:fade={{ duration: 200 }}
      in:fade={{ duration: 200 }}
    >
      <div class="item-main">
        <span class="item-quantity">{item.qty}×</span>
        <div class="item-main-content">
          <p data-testid={`cart-item-${item.desc}`}>
            {item.desc}
          </p>
          {#if item.modifiers?.length}
            <span class="modifier-list">
              <ul>
                {#each item.modifiers as modifier}
                  {@render selectedModifiers(modifier)}
                {/each}
              </ul>
            </span>
          {/if}
          <div class="item-actions">
            {#snippet modalContent()}
              <ItemView
                detailedItem={detailedItemClone!}
                modifierListState={new ModifierListState(
                  item.modifierListState,
                )}
                editing={true}
                initialQuantity={item.qty}
                onAddToCart={({ item: updatedItem }) => {
                  const cartItemToUpdate = {
                    ...updatedItem,
                    cartItemId: item.cartItemId,
                    modifierListState: item.modifierListState,
                  };
                  cart.updateCartItem(cartItemToUpdate);
                  cart.updateModifierListState(
                    item.cartItemId,
                    new ModifierListState(item.modifierListState),
                  );
                  editItemModal?.closeModal();
                  cart.vocalizeUpdateForSR();
                }}
              />
            {/snippet}
            <button
              onclick={() => {
                const { desc } = item;
                originalQuantity = item.qty;
                detailedItemClone = $state.snapshot(item);
                editItemModal?.open({
                  title: desc,
                  description: item.modifierDesc ?? "",
                  children: modalContent,
                  onClose: () => {
                    item.qty = originalQuantity;
                    detailedItemClone = null;
                  },
                });
              }}
              aria-label="{index === 0
                ? 'Viewing cart items. '
                : ''}Edit {item.desc} quantity"
              class="action-button"
              >Edit
            </button>
            <button
              aria-label="Remove {item.desc} from cart"
              class="action-button"
              onclick={() => {
                cart.remove(item.cartItemId);
              }}
            >
              Remove
            </button>
          </div>
        </div>
        <span class="item-price">
          {centToDollar(calculateTotalItemPrice(item))}
        </span>
      </div>
    </li>
  {/each}
</ul>
<div id="quantity-live-region" class="sr-only" aria-live="polite"></div>

<Modal bind:this={editItemModal} />

<style>
  .cart-items {
    flex: 1;
    margin: 0;
    padding: var(--padding-sm);
    overflow-y: auto;
  }

  .cart-item {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid var(--gray-200);
    padding: 10px 0;
    width: 100%;
  }

  .item-main {
    display: flex;
    justify-content: space-between;
    align-items: start;
    margin-bottom: 5px;
  }

  .item-quantity {
    margin-right: 10px;
  }

  .item-main-content {
    display: flex;
    flex: 1;
    flex-direction: column;
    font-weight: 500;
  }

  .item-actions {
    display: flex;
    gap: 10px;
    margin-top: var(--margin-sm);
  }

  .action-button {
    border: none;
    background: none;
    padding: 0;
    color: var(--primary-color);
    font-size: 14px;
    text-decoration: underline;
  }

  .modifier-list {
    color: var(--gray-700);
    font-size: var(--font-xs);
  }

  ul {
    padding: 0;
    font-weight: normal;
    list-style: none;
  }

  .sublist {
    padding-inline-start: var(--padding-md);
  }

  .sublist li::before {
    content: "⊢";
    color: var(--gray-300);
  }
</style>
