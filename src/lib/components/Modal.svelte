<!--suppress JSUnresolvedReference -->
<script lang="ts">
  import { createDialog, type CreateDialogProps, melt } from "@melt-ui/svelte";
  import { type Snippet } from "svelte";
  import { fade } from "svelte/transition";
  import CloseIcon from "~icons/mdi/close";

  let title = $state("");
  let description = $state<string>("");
  let children = $state<Snippet>();
  let onClose = $state<(() => void) | undefined>(undefined);
  let modalEl: HTMLDivElement | undefined = $state(undefined);

  const handleOpen: CreateDialogProps["onOpenChange"] = ({ curr, next }) => {
    if (curr === true && next === false) {
      onClose?.();
    }
    return next;
  };

  const {
    elements: {
      portalled,
      overlay,
      content,
      title: modalTitle,
      description: modalDescription,
      close,
    },
    states: { open: isOpen },
  } = createDialog({
    onOpenChange: handleOpen,
  });

  export const open = (props: {
    title: string;
    children: Snippet;
    description?: string;
    onClose?: () => void;
  }) => {
    title = props.title;
    children = props.children;
    description = props.description ?? "";
    onClose = props.onClose;
    isOpen.set(true);
  };

  export const closeModal = () => {
    isOpen.set(false);
  };

  export const setModalHeight = (height: "sm" | "lg") => {
    const heightToSet = height === "lg" ? "800px" : "400px";

    modalEl?.style.setProperty("--modal-height", heightToSet);
  };
</script>

{#snippet header({
  title,
  _showBack,
  showClose,
}: {
  title: string;
  _showBack?: boolean;
  showClose?: boolean;
})}
  <div class="modal-header">
    <h2 use:melt={$modalTitle}>{title}</h2>

    {#if showClose}
      <button
        aria-label="Close Modal"
        onclick={closeModal}
        use:melt={$close}
        class="close-button"
      >
        <CloseIcon />
      </button>
    {/if}
  </div>
{/snippet}

{#if $isOpen}
  <div use:melt={$portalled}>
    <div
      class="overlay"
      use:melt={$overlay}
      transition:fade={{ duration: 100 }}
    ></div>
    <div class="modal-wrapper">
      <div
        bind:this={modalEl}
        class="modal"
        use:melt={$content}
        transition:fade={{ duration: 100 }}
      >
        {#if title}
          {@render header({ title, showClose: true })}
        {/if}
        <section class="modal-body">
          {#if description}
            <p class="description" use:melt={$modalDescription}>
              {description}
            </p>
          {/if}
          {#if children}
            {@render children()}
          {/if}
        </section>
      </div>
    </div>
  </div>
{/if}

<style>
  .modal-wrapper {
    display: flex;
    position: fixed;
    justify-content: center;
    align-items: start;
    inset: 0;
    padding: 5rem;
    pointer-events: none;
  }

  .modal {
    --modal-height: 800px;
    display: flex;

    position: relative;
    flex-direction: column;
    transition: height 0.3s ease-in-out;
    border-radius: var(--radius-md);
    background-color: white;
    width: var(--screen-sm);
    height: var(--modal-height);
    max-height: 100%;
    overflow: hidden;
    pointer-events: auto;
  }

  .modal-header {
    display: flex;
    align-items: center;
    padding: var(--padding-md);
  }

  .modal-body {
    display: flex;
    position: relative;
    flex-direction: column;
    height: 100%;
    overflow-y: hidden;
  }

  .close-button {
    transition: ease-in-out 0.3s;
    margin-left: auto;
    border: none;
    background: none;
  }

  .close-button:hover {
    color: var(--red-500);
  }

  .description {
    padding-inline: var(--padding-md);
    padding-block-end: var(--padding-md);
  }

  h2 {
    font-weight: bold;
  }

  @media (max-width: 640px) {
    .modal-wrapper {
      padding: 2rem;
    }
  }
</style>
