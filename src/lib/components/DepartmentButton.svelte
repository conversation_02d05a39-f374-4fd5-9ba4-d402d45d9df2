<script lang="ts">
  const { name }: { name: string } = $props();
</script>

<a href="#{name.toLowerCase().replace(/ /g, '-')}" class="department-button"
  >{name}
</a>

<style>
  .department-button {
    padding: var(--padding-xs) var(--padding-sm);
    background-color: transparent;
    white-space: nowrap;
    font-size: var(--font-md);
    text-decoration: none;
    color: var(--gray-900);
  }

  .department-button:focus {
    outline: var(--primary-color) auto 1px;
  }
</style>
