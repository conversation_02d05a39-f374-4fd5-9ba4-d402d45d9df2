<script lang="ts">
  import { createTooltip, melt } from "@melt-ui/svelte";
  import type { Snippet } from "svelte";

  let {
    content,
    position = "top",
    children,
    arrowSize = 8,
    maxWidth = 250,
  }: {
    content: string;
    position?: "top" | "bottom" | "left" | "right";
    children: Snippet;
    arrowSize?: number;
    maxWidth?: number;
  } = $props();

  const {
    elements: { trigger, content: tooltipContent, arrow },
    states: { open },
  } = createTooltip({
    positioning: {
      placement: position,
    },
    arrowSize,
    openDelay: 300,
    closeDelay: 100,
  });
</script>

<!--adheres to the WAI-ARIA tooltip role design pattern-->
<div class="desktop-only">
  <div use:melt={$trigger} class="tooltip-trigger">
    {@render children()}
  </div>

  {#if $open}
    <div
      use:melt={$tooltipContent}
      class="tooltip-content"
      style="max-width: {maxWidth}px;"
    >
      <div use:melt={$arrow}></div>
      {content}
    </div>
  {/if}
</div>

<style>
  .tooltip-trigger {
    cursor: pointer;
    display: flex;
    align-items: center;
  }

  .tooltip-content {
    background-color: var(--gray-950);
    color: white;
    border-radius: var(--radius-md);
    padding: var(--padding-sm) var(--padding-md);
    box-shadow: var(--shadow-elevation-medium);
    font-size: var(--font-sm);
  }
</style>
