<script lang="ts">
  import { centToDollar } from "$lib/helpers/number.ts";
  import type { <PERSON><PERSON>ventHand<PERSON> } from "svelte/elements";

  const {
    name,
    description = "",
    price,
    onclick,
    highlightTerm,
    isInStock,
  }: {
    name: string;
    description?: string;
    price: number;
    onclick: MouseEventHandler<HTMLButtonElement>;
    highlightTerm: string;
    isInStock: boolean;
  } = $props();

  const highlightText = (text: string, term: string) => {
    if (!term.trim()) return [{ text, highlight: false }];
    const regex = new RegExp(`(${term})`, "gi");
    const parts = text.split(regex);
    return parts.map((part) => ({
      text: part,
      highlight: part.toLowerCase() === term.toLowerCase(),
    }));
  };

  let nameParts = $derived(highlightText(name, highlightTerm));
  let descriptionParts = $derived(
    highlightText(description || "", highlightTerm),
  );
</script>

<button class="item-card" {onclick} disabled={!isInStock}>
  <hgroup>
    <h3 class="item-title">
      {#each nameParts as part}
        {#if part.highlight}
          <mark>{part.text}</mark>
        {:else}
          {part.text}
        {/if}
      {/each}
    </h3>
    {#if description}
      <p class="item-description">
        {#each descriptionParts as part}
          {#if part.highlight}
            <mark>{part.text}</mark>
          {:else}
            {part.text}
          {/if}
        {/each}
      </p>
    {/if}
  </hgroup>
  <div class="card-footer">
    <span class="item-price" data-price-varies={price === 0 ? "" : undefined}>
      {price === 0 ? "Price varies with options" : centToDollar(price)}
    </span>
    {#if !isInStock}
      <span class="out-of-stock-badge">Out of Stock</span>
    {/if}
  </div>
</button>

<style>
  .item-card {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    box-shadow: var(--shadow-elevation-low);
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    background-color: white;
    padding: var(--padding-sm);
    text-align: left;

    --text-color: var(inherit);

    &:disabled {
      --text-color: var(--gray-500);
    }

    &:disabled .item-price {
      text-decoration: line-through;
    }
  }

  .item-card:hover {
    border-color: var(--primary-color);
  }

  .item-card hgroup {
    display: flex;
    flex-direction: column;
    align-items: start;
    width: 100%;
  }

  .item-title {
    padding: var(--padding-sm);
    padding-block-end: 0;
    color: var(--gray-950);
    font-weight: 500;
  }

  .item-description {
    padding: var(--padding-sm);
    color: var(--gray-800);
  }

  .item-price {
    padding: var(--padding-sm);
    color: var(--text-color);
    font-size: var(--font-md);
  }

  .out-of-stock-badge {
    align-self: center;
    margin-left: auto;
    border-radius: 4px;
    background-color: var(--red-100);
    padding: var(--padding-xs);
    color: var(--red-500);
    font-size: var(--font-sm);
  }

  .card-footer {
    display: flex;
    align-items: center;
  }

  .item-price[data-price-varies] {
    color: var(--gray-700);
  }

  mark {
    background-color: yellow;
    padding: 0;
    color: inherit;
  }
</style>
