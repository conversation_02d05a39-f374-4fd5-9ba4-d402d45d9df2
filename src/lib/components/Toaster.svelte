<script lang="ts">
  import type { Toast } from "$lib/services/ToastManager.svelte";
  import { toast } from "$lib/services/ToastManager.svelte";
  import {
    flyFrom,
    getContainerStyle,
    getToastStyle,
  } from "$lib/helpers/toast-helpers";
  import { flip } from "svelte/animate";
  import { quintOut } from "svelte/easing";
  import { fly } from "svelte/transition";
  import WarningIcon from "~icons/mdi/alert";
  import ErrorIcon from "~icons/mdi/alert-circle";
  import SuccessIcon from "~icons/mdi/check-circle";
  import CloseIcon from "~icons/mdi/close";
  import InfoIcon from "~icons/mdi/information";

  let toasts = toast.getToasts();

  const handleClose = (toastItem: Toast) => {
    toast.removeToast(toastItem.id);
  };

  const autofocus = (node: HTMLElement, shouldFocus: boolean) => {
    if (shouldFocus) {
      node.focus();
    }
  };

  const positions = $derived(
    Array.from(new Set(toasts.map((toast) => toast.position))),
  );
</script>

{#each positions as position}
  <header
    class="toast-container"
    style={getContainerStyle(position)}
    transition:fly={{
      y: flyFrom(position),
      duration: 300,
      easing: quintOut,
    }}
  >
    {#each toasts.filter((t) => t.position === position) as toast (toast.id)}
      <div
        class="toast-wrapper"
        transition:fly={{
          y: flyFrom(toast.position),
          duration: 300,
          easing: quintOut,
        }}
        animate:flip={{ duration: 300 }}
      >
        {#if toast}
          <div
            class="toast"
            class:title={!!toast.title}
            style={getToastStyle(toast.type)}
          >
            <div aria-live="assertive" aria-atomic="true" class="sr-only">
              {#if toast.title}
                {toast.title}:
              {/if}
              {toast.message}
            </div>
            <div class="toast-content">
              <div class="icon-container">
                <div class="icon">
                  {#if toast.type === "success"}
                    <SuccessIcon color="green" />
                  {:else if toast.type === "error"}
                    <ErrorIcon color="red" />
                  {:else if toast.type === "warning"}
                    <WarningIcon color="#fcba03" />
                  {:else if toast.type === "info"}
                    <InfoIcon color="blue" />
                  {/if}
                </div>
              </div>
              <div class="text-content">
                {#if toast.title}
                  <div class="title">{toast.title}</div>
                {/if}
                <div class="message">{toast.message}</div>
              </div>
            </div>

            {#if toast.dismissible}
              <button
                aria-label="Close notification"
                class="close-button"
                data-toast-id={toast.id}
                onclick={() => handleClose(toast)}
                use:autofocus={toast.focused}
              >
                <CloseIcon />
              </button>
            {/if}
          </div>
        {/if}
      </div>
    {/each}
  </header>
{/each}

<style>
  .toast-wrapper {
    position: relative;
    margin-bottom: var(--margin-sm);
  }

  .toast {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: var(--padding-md);
    border-radius: var(--radius-md);
    border: 1px solid;
    box-shadow: var(--shadow-elevation-high);
    min-width: 320px;
  }

  .toast-content {
    display: flex;
    align-items: center;
    gap: var(--gap-md);
    flex: 1;
  }

  .toast.title .toast-content {
    align-items: flex-start;
  }

  .icon-container {
    display: flex;
    width: 2rem;
    height: 2rem;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: white;
    box-shadow: var(--shadow-elevation-medium);
    flex-shrink: 0;
  }

  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 17px;
    height: 17px;
  }

  .text-content {
    display: flex;
    flex-direction: column;
  }

  .title {
    font-weight: 600;
    margin-bottom: var(--margin-md);
    text-align: start;
  }

  .message {
    font-weight: 600;
  }

  .toast.title .message {
    font-weight: 400;
  }

  .close-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    align-self: center;

    &:hover {
      color: rgba(0, 0, 0, 0.3);
      transition: all 0.2s ease-in-out;
    }

    &:focus {
      outline: 1px solid var(--primary-color);
      border-radius: var(--radius-sm);
    }
  }

  .toast.title .close-button {
    align-self: flex-start;
  }
</style>
