<script lang="ts">
  interface Props {
    title: string;
    onClick: () => Promise<void> | void;
    disabled?: boolean;
    isLoading?: boolean;
  }

  let { title, onClick, disabled = false, isLoading = false }: Props = $props();

  const handleClick = async () => {
    if (disabled || isLoading) return;
    await onClick();
  };
</script>

<button
  aria-label={title}
  class="button"
  onclick={handleClick}
  disabled={disabled || isLoading}
>
  <span class="button-text">{title}</span>
  {#if isLoading}
    <span class="loading-indicator" aria-hidden="true"></span>
    <span class="sr-only">Loading...</span>
  {/if}
</button>

<style>
  .button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: var(--padding-md);
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--font-lg);
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    transition:
      background-color 0.2s ease-in-out,
      transform 0.1s ease-in-out;
  }

  .button:hover:not(:disabled) {
    background-color: var(--primary-color-hover);
  }

  .button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .button-text {
    flex: 1;
  }

  .loading-indicator {
    width: 20px;
    height: 20px;
    margin-left: var(--margin-md);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
</style>
