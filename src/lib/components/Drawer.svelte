<script lang="ts">
  import { browser } from "$app/environment";
  import { createDialog, melt } from "@melt-ui/svelte";
  import { type Snippet } from "svelte";
  import { quintOut } from "svelte/easing";
  import { fly } from "svelte/transition";
  import CloseIcon from "~icons/mdi/close";

  // We may want to make this global in the future (use case for get and set context?)
  const prefersReducedMotion = browser
    ? window?.matchMedia("(prefers-reduced-motion: reduce)").matches
    : undefined;

  let {
    children,
    setOverlay = true,
    title = "",
    onClose,
  }: {
    children?: Snippet;
    setOverlay?: boolean;
    title?: string;
    onClose?: () => void;
  } = $props();

  let drawerElement = $state<HTMLElement | null>(null);

  const {
    elements: { portalled, title: modalTitle, overlay, content },
    states: { open: isOpen },
  } = createDialog();

  export const open = (props?: { title: string; children: Snippet }) => {
    if (props) {
      title = props.title;
      children = props.children;
    }
    isOpen.set(true);
  };
  export const close = () => {
    if (!$isOpen) return;
    isOpen.set(false);
    if (onClose) {
      onClose();
    }
  };
</script>

{#if $isOpen}
  <div use:melt={$portalled}>
    <div class={setOverlay ? "overlay" : ""} use:melt={$overlay}></div>
    <div class="drawer-container">
      <div
        bind:this={drawerElement}
        use:melt={$content}
        class="drawer"
        transition:fly={{
          y: drawerElement.clientHeight,
          duration: prefersReducedMotion ? 0 : 400,
          opacity: 0.5,
          easing: quintOut,
        }}
      >
        <div class="drawer-header">
          <h2 use:melt={$modalTitle} class="drawer-title">{title}</h2>
          <button onclick={close} aria-label="Close" class="close-button">
            <CloseIcon />
          </button>
        </div>

        <div class="drawer-content">
          {@render children?.()}
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  .drawer-container {
    display: flex;
    position: fixed;
    bottom: 0;
    inset-inline: 0;
    height: clamp(400px, 700px, 90dvh);
    pointer-events: none;
  }

  .drawer {
    display: flex;
    flex-direction: column;
    box-shadow: var(--shadow-elevation-medium);
    border-radius: var(--radius-md) var(--radius-md) 0 0;
    background-color: var(--gray-50);
    width: 100%;
    overflow: hidden;
    pointer-events: auto;
  }
  .drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--padding-md);
  }
  .drawer-title {
    margin: 0;
    color: var(--gray-950);
    font-weight: 600;
    font-size: var(--font-lg);
  }
  .drawer-content {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    padding-bottom: 0;
    overflow-y: auto;
  }
  .close-button {
    display: flex;
    flex-shrink: 0;
    background: none;
    font-size: var(--font-lg);
  }
</style>
