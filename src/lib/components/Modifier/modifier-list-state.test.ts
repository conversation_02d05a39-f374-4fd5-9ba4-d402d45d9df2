import type { DetailedItem } from "$lib/types";
import { beforeEach, describe, expect, it } from "vitest";
import { ModifierListState } from "./modifier-list-state.svelte";

describe("ModifierListState", () => {
  let modifierListState: ModifierListState;

  beforeEach(() => {
    modifierListState = new ModifierListState();
  });

  describe("constructor", () => {
    it("should initialize with empty state when no arguments provided", () => {
      expect(modifierListState.modifierInputs).toEqual({});
      expect(modifierListState.validationStates).toEqual({});
    });

    it("should initialize with provided state", () => {
      const initialState = {
        modifierInputs: { "test-id": true },
        validationStates: { "test-id": "valid" as const },
      };

      const state = new ModifierListState(initialState);
      expect(state.modifierInputs).toEqual(initialState.modifierInputs);
      expect(state.validationStates).toEqual(initialState.validationStates);

      // Make sure the object was cloned deeply and doesn't contain old references.
      expect(state.modifierInputs).not.toBe(initialState.modifierInputs);
      expect(state.validationStates).not.toBe(initialState.validationStates);
    });
  });

  describe("clear", () => {
    it("should reset all state to empty objects", () => {
      // Set some initial state
      modifierListState.modifierInputs = { "test-id": true };
      modifierListState.validationStates = { "test-id": "valid" };

      // Clear the state
      modifierListState.clear();

      // Verify state is cleared
      expect(modifierListState.modifierInputs).toEqual({});
      expect(modifierListState.validationStates).toEqual({});
    });
  });

  describe("getItemWithModifierListState", () => {
    it("should return a new item with modifier list state attached", () => {
      const item: DetailedItem = {
        item: "test-item",
        desc: "Test Item",
        price: 10,
        multiModLists: false,
        modifiers: [],
        modMaxSel: 0,
        modMinSel: 0,
        count: 3,
        isVisible: true,
        qty: 1,
        selected: true,
      };

      // Set some initial state
      modifierListState.modifierInputs = { "test-id": true };
      modifierListState.validationStates = { "test-id": "valid" };

      const result = modifierListState.getItemWithModifierListState(item);

      // Verify the item is cloned and has the modifier list state
      expect(result).toEqual({
        ...item,
        modifierListState: expect.any(ModifierListState),
      });

      // Verify the modifier list state is cloned
      expect(result.modifierListState.modifierInputs).toEqual(
        modifierListState.modifierInputs,
      );
      expect(result.modifierListState.validationStates).toEqual(
        modifierListState.validationStates,
      );

      // Verify it's a new instance
      expect(result.modifierListState).not.toBe(modifierListState);
    });
  });
});
