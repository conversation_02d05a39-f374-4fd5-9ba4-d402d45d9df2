<script lang="ts">
  import { getStackedSnippetState } from "$lib/services/StackedSnippetState.svelte.js";
  import type { DetailedItem } from "../../types/index";
  import Modifier from "./Modifier.svelte";

  let {
    modifierList,
    disableList,
    renderAsLabels,
    isStacked,
    isValid,
  }: {
    modifierList: DetailedItem[];
    disableList?: boolean;
    renderAsLabels: boolean;
    isStacked: boolean;
    isValid?: boolean;
  } = $props();

  const { pop: popStackedSnippet } = getStackedSnippetState();
</script>

<div class="modifier-list" class:space-between={renderAsLabels}>
  {#each modifierList as modifier}
    <Modifier {modifier} {disableList} renderAsLabel={renderAsLabels} />
  {/each}
</div>

{#if isStacked}
  <div class="confirm-choice-button-wrapper">
    <button
      class="button--primary-lg"
      disabled={!isValid}
      onclick={popStackedSnippet}
    >
      Confirm choice
    </button>
  </div>
{/if}

<style>
  .confirm-choice-button-wrapper {
    padding: var(--padding-md);
    padding-top: 0;
  }

  .modifier-list {
    height: 100%;
    overflow-y: auto;
  }

  .space-between {
    :global(& > :not(:last-child)) {
      margin-block-end: var(--margin-md);
    }
    overflow-y: auto;
  }
</style>
