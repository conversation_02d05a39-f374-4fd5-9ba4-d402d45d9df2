<script lang="ts">
  import { hasSomeSelectedChildren } from "$lib/helpers/item.ts";
  import { centToDollar } from "$lib/helpers/number.ts";
  import { getStackedSnippetState } from "$lib/services/StackedSnippetState.svelte.js";
  import { slide } from "svelte/transition";
  import { getModifierListState } from "./modifier-list-state.svelte";
  import ModifierList from "./ModifierList.svelte";
  import RequirementLabel from "./RequirementLabel.svelte";
  import {
    getSelectedChildren,
    getValidationState,
    hasChildrenAndIsSelected,
    hasIncompleteRequiredChildSelections,
    shouldRenderChildrenAsLabels,
  } from "$lib/components/Modifier/helpers.ts";
  import type { DetailedItem } from "$lib/types";

  let {
    renderAsLabel,
    modifier,
    disableList,
  }: {
    renderAsLabel: boolean;
    modifier: DetailedItem;
    disableList?: boolean;
  } = $props();

  const stackedSnippetState = getStackedSnippetState();
  const disabled = $derived(
    !modifier.selected && (modifier.count === 0 || disableList),
  );
  let modifierListState = getModifierListState();
  let validationStates = modifierListState.validationStates;

  const renderAsInput = $derived(!renderAsLabel);

  const shouldRenderRequirementLabel = $derived(
    !shouldRenderChildrenAsLabels(modifier, renderAsLabel) &&
      (renderAsLabel || hasChildrenAndIsSelected(modifier)),
  );

  $effect(() => {
    validationStates[modifier.item] = validationState;
  });

  $effect(() => {
    // Force labels to be selected if they have selected children
    if (renderAsLabel && hasSomeSelectedChildren(modifier)) {
      modifier.selected = true;
    }
  });

  const initInput = (_el: HTMLInputElement, modifier: DetailedItem) => {
    if (modifier.selected === undefined) {
      modifier.selected = false;
    }
  };

  const shouldDisableChildModifiers = ({
    modifier,
  }: {
    modifier: DetailedItem;
  }) => {
    // If there's no selection limits, don't disable
    if (!modifier.modMaxSel && !modifier.modMinSel) {
      return false;
    }

    // Get count of currently selected children
    const selectedChildren = getSelectedChildren(modifier).length;

    // If we've hit the max selection limit, disable unselected items
    return !!(modifier.modMaxSel && selectedChildren >= modifier.modMaxSel);
  };

  const validationState = $derived(
    getValidationState({ modifier, renderAsInput }),
  );

  const unselectAllChildren = (modifier: DetailedItem) => {
    if (modifier.modifiers) {
      for (const child of modifier.modifiers) {
        child.selected = false;
        unselectAllChildren(child);
      }
    }
  };
</script>

{#snippet childModifiers({
  modifier,
  isStacked = false,
}: {
  modifier: DetailedItem;
  isStacked?: boolean;
})}
  {#if modifier.modifiers}
    <ModifierList
      modifierList={modifier.modifiers}
      renderAsLabels={shouldRenderChildrenAsLabels(modifier, renderAsLabel)}
      disableList={shouldDisableChildModifiers({
        modifier,
      })}
      {isStacked}
      isValid={["", "valid"].includes(validationState)}
    />
  {/if}
{/snippet}

{#snippet selectedChildModifiers({ modifier }: { modifier: DetailedItem })}
  {#if modifier.selected && renderAsInput && hasSomeSelectedChildren(modifier)}
    <ul>
      {#each modifier?.modifiers ?? [] as childModifier}
        {#if childModifier.selected}
          <li>
            <div class="selected-child-modifier">
              {childModifier.modifierDesc || childModifier.desc}
              {#if childModifier.price}
                <div class="modifier-price">
                  + {centToDollar(childModifier.price)}
                </div>
              {/if}
            </div>
          </li>
          {@render selectedChildModifiers({ modifier: childModifier })}
        {/if}
      {/each}
    </ul>
  {/if}
{/snippet}

<div class="modifier-wrapper">
  <label
    class:modifier-label={renderAsLabel}
    class:modifier-text={!renderAsLabel}
    class:disabled-modifier={!renderAsLabel && disabled && !modifier.selected}
  >
    <div class="modifier-content">
      <div class="modifier-info">
        <div class="modifier-title">
          {modifier.modifierDesc || modifier.desc}
        </div>
        {#if modifier.price && renderAsInput}
          <span class="modifier-price">+ {centToDollar(modifier.price)}</span>
        {/if}
      </div>
      {#if shouldRenderRequirementLabel}
        <RequirementLabel {modifier} {modifierListState} />
      {/if}
    </div>
    {#if renderAsInput}
      <input
        aria-label={modifier.modifierDesc || modifier.desc}
        checked={modifier.selected}
        {disabled}
        onchange={(e) => {
          const checked = e.currentTarget.checked;
          modifier.selected = checked;
          if (!checked) {
            unselectAllChildren(modifier);
          } else {
            // Set quantity to 1 when selected so price can be calculated effectively
            modifier.qty = 1;
          }
        }}
        use:initInput={modifier}
        type="checkbox"
      />
    {/if}
  </label>

  {@render selectedChildModifiers({ modifier })}
  {#if renderAsLabel}
    <div class="nested">
      {@render childModifiers({ modifier })}
    </div>
  {:else if hasChildrenAndIsSelected(modifier) && modifier.selected}
    <div transition:slide class="nested-selection-button-wrapper">
      <button
        class="button--secondary"
        onclick={(e) => {
          e.preventDefault();
          if (!modifier.selected) {
            modifier.selected = true;
          }
          stackedSnippetState.push({
            snippet: childModifiers,
            snippetProps: { modifier, isStacked: true },
            title: modifier.modifierDesc || modifier.desc,
            HeaderSnippet: shouldRenderRequirementLabel
              ? RequirementLabel
              : undefined,
            headerSnippetProps: shouldRenderRequirementLabel
              ? { modifier, modifierListState }
              : undefined,
          });
        }}
      >
        {#if hasIncompleteRequiredChildSelections(modifier)}
          Selections required
          <span class="required-star"></span>
        {:else}
          Choose selections
        {/if}
      </button>
    </div>
  {/if}
</div>

<style>
  .modifier-wrapper {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid var(--gray-200);
    width: 100%;

    &:last-child {
      border-bottom: none;
    }
  }

  .modifier-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .modifier-price {
    align-self: flex-start;
    margin-left: var(--gap-md);
    color: var(--gray-900);
    font-weight: bold;
    white-space: nowrap;
  }

  .modifier-text {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: var(--gray-950);
  }

  .nested {
    transition: all 0.3s ease;
    background-color: white;
  }

  .nested-selection-button-wrapper {
    padding: var(--padding-sm) var(--padding-md);
  }

  label {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin: 0;
    padding: var(--padding-md);
  }

  .modifier-label {
    position: sticky;
    top: 0;
    box-shadow: var(--shadow-elevation-low);
    background-color: white;
    color: var(--gray-950);
    font-weight: bold;
    font-size: large;
  }

  .modifier-content {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: var(--gap-sm);
  }

  .modifier-title {
    transition: all 0.2s ease;
    font-size: var(--font-md);
  }

  .disabled-modifier {
    cursor: not-allowed;
    color: var(--gray-400);
  }

  .disabled-modifier .modifier-price {
    color: var(--gray-400);
  }

  input[type="checkbox"] {
    cursor: pointer;
    margin-top: 2px;
    margin-left: var(--margin-md);
    border: 3px solid var(--gray-300);
    width: 18px;
    height: 18px;

    &:checked {
      border-color: var(--primary-color);
      background-color: var(--primary-color);
    }

    &:disabled {
      cursor: not-allowed;
    }
  }

  ul {
    margin-left: var(--margin-md);
    padding: 0;
    list-style: none;
  }

  ul::before {
    margin: 0;
    padding: 0;
    list-style-type: none;
  }

  li {
    display: flex;
    align-items: center;
  }

  li::before {
    content: "⊢";
    color: var(--gray-300);
  }

  .selected-child-modifier {
    display: flex;
    flex: 1;
    justify-content: space-between;
    padding-right: var(--padding-md);
    font-size: var(--font-sm);

    @media (min-width: 768px) {
      padding-right: var(--padding-xl);
    }
  }
</style>
