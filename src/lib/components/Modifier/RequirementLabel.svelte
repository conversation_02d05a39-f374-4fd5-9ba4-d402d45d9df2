<script lang="ts" module>
  import type { ModifierListState } from "./modifier-list-state.svelte";

  const isRequired = (modifier: DetailedItem) => modifier.modMinSel;

  const hasSingleValidValue = (modifier: DetailedItem) =>
    modifier.modMinSel !== undefined &&
    modifier.modMinSel === modifier.modMaxSel &&
    modifier.modMaxSel !== 0;

  const hasMultipleValidValues = (modifier: DetailedItem) =>
    modifier.modMinSel !== undefined ||
    (modifier.modMaxSel !== undefined && modifier.modMaxSel !== 0);
</script>

<script lang="ts">
  import type { DetailedItem } from "$lib/types";

  let {
    modifier,
    modifierListState,
  }: { modifier: DetailedItem; modifierListState: ModifierListState } =
    $props();

  const requirementClass = $derived(
    modifier.modMinSel ? "required" : "optional",
  );
  const validationClass: "valid" | "invalid" | "" = $derived(
    modifierListState.validationStates[modifier.item] ?? "",
  );
</script>

<div class="requirements-wrapper">
  <p class="requirements {requirementClass} {validationClass}">
    {#if isRequired(modifier)}
      <span class="requirement-tag">Required</span>
    {:else}
      <span class="requirement-tag optional">Optional</span>
    {/if}
    {#if hasSingleValidValue(modifier)}
      <span class="selection-info">Select {modifier.modMinSel}</span>
    {:else if hasMultipleValidValues(modifier)}
      <span class="selection-info"
        >Select {modifier.modMinSel || "up"} to
        {modifier.modMaxSel === 0
          ? "as many as you want"
          : modifier.modMaxSel}</span
      >
    {/if}
  </p>
</div>

<style>
  .requirements-wrapper {
    display: inline-flex;
    justify-content: flex-start;
    align-items: center;
  }

  .requirements {
    display: flex;
    align-items: center;
    gap: var(--gap-sm);
    transition: all 0.3s ease;
    margin: 0;
    font-size: var(--font-xs);
  }

  .requirement-tag {
    display: inline-block;
    border-radius: var(--radius-sm);
    background-color: var(--green-75);
    padding: 2px 6px;
    color: var(--green-500);
    font-weight: 600;
    font-size: var(--font-xs);
    letter-spacing: 0.5px;
    text-transform: uppercase;
  }

  .requirement-tag.optional {
    background-color: var(--gray-200);
    color: var(--gray-950);
  }

  .selection-info {
    color: var(--gray-950);
    font-weight: 500;
  }

  .requirements.valid:not(.optional) .requirement-tag {
    background-color: var(--green-75);
    color: var(--green-500);
  }

  .requirements.valid:not(.optional) .selection-info {
    color: var(--green-500);
  }

  .requirements.invalid .requirement-tag {
    background-color: var(--red-75);
    color: var(--red-600);
  }

  .requirements.invalid .selection-info {
    color: var(--red-600);
  }
</style>
