import type { DetailedItem } from "$lib/types";

/**
 * Determines if the children of a modifier should be rendered as labels
 * @param modifier - The modifier to check
 * @param modifierIsLabel - Whether the modifier is a label
 * @returns True if the parent modifier has 'multiModLists' set to true and is not a label
 */
export const shouldRenderChildrenAsLabels = (
  modifier: DetailedItem,
  modifierIsLabel: boolean,
) => {
  if (!modifier.multiModLists || modifierIsLabel) {
    return false;
  }

  return modifier.modifiers.length > 0;
};

export const getSelectedChildren = (modifier: DetailedItem): DetailedItem[] =>
  modifier.modifiers?.filter((m) => m.selected) ?? [];

export const hasChildrenAndIsSelected = (modifier: DetailedItem) =>
  modifier.modifiers.length > 0 && modifier.selected;

export const allChildrenResolved = (
  parent: DetailedItem,
  parentRenderAsInput: boolean,
) => {
  const childrenShouldRenderAsLabels = shouldRenderChildrenAsLabels(
    parent,
    !parentRenderAsInput,
  );
  const childShouldRenderAsInput = !childrenShouldRenderAsLabels;
  return parent.modifiers.every((child) => {
    const childValidationState = getValidationState({
      modifier: child,
      renderAsInput: childShouldRenderAsInput,
    });
    return childValidationState !== "invalid";
  });
};

/**
 * If a modifier is multiModLists true => it's children are labels
 */
export const childIsARequiredLabel = (modifier: DetailedItem) => {
  return (
    modifier.selected &&
    modifier.multiModLists &&
    modifier.modifiers.some((child) => child.modMinSel && !child.selected)
  );
};

export const getValidationState = ({
  modifier,
  renderAsInput,
}: {
  modifier: DetailedItem;
  renderAsInput: boolean;
}): "" | "valid" | "invalid" => {
  // Unselected checkboxes are automatically resolved (this is redundant to `shouldBeValidated` but is here for safety and clarity)
  if (renderAsInput && !modifier.selected) {
    return "";
  }

  // If this modifier is a selected checkbox with no children, it's resolved
  if (!modifier.modifiers) {
    return "";
  }

  const selectedChildren = getSelectedChildren(modifier);

  const minimumSatisfied = selectedChildren.length >= modifier.modMinSel;
  // 0 is unlimited
  const maximumSatisfied =
    modifier.modMaxSel === 0
      ? true
      : selectedChildren.length <= modifier.modMaxSel;

  if (
    !minimumSatisfied ||
    !maximumSatisfied ||
    childIsARequiredLabel(modifier)
  ) {
    return "invalid";
  }

  // If minimum and maximum are satisfied, and every child is resolved, return true
  return allChildrenResolved(modifier, renderAsInput) ? "valid" : "invalid";
};

export const hasIncompleteRequiredChildSelections = (modifier: DetailedItem) =>
  modifier.selected &&
  modifier.modifiers.some((child) => child.modMinSel > 0 && !child.selected);
