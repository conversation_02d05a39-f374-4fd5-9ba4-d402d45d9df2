import type { DetailedItem } from "$lib/types";
import { getContext, setContext } from "svelte";

export class ModifierListState {
  /**
   * A record of modifier input ids and their values (checked or unchecked)
   */
  modifierInputs: Record<DetailedItem["item"], boolean> = $state({});

  validationStates: Record<DetailedItem["item"], "valid" | "invalid" | ""> =
    $state({});

  /**
   * If an existing modifier list state is passed, will create a new instance with deeply copied properties
   */
  constructor(modifierListState?: {
    modifierInputs: Record<DetailedItem["item"], boolean>;
    validationStates: Record<DetailedItem["item"], "valid" | "invalid" | "">;
  }) {
    if (modifierListState) {
      this.modifierInputs = $state.snapshot(modifierListState.modifierInputs);
      this.validationStates = $state.snapshot(
        modifierListState.validationStates,
      );
    }
  }

  clear() {
    this.modifierInputs = {};
    this.validationStates = {};
  }

  /**
   * Returns a new item with the modifier list state attached
   */
  getItemWithModifierListState = (item: DetailedItem) => {
    const itemCopy: DetailedItem & { modifierListState: ModifierListState } = {
      ...structuredClone(item),
      modifierListState: new ModifierListState(this),
    };

    return itemCopy;
  };

  get allModifiersAreValid() {
    return Object.values(this.validationStates).every(
      (state) => state !== "invalid",
    );
  }

  get someModifiersAreInvalid() {
    return !this.allModifiersAreValid;
  }
}

const MODIFIER_LIST_KEY = Symbol("MODIFIER_LIST");

export const setModifierListState = (modifierListState?: ModifierListState) => {
  return setContext(
    MODIFIER_LIST_KEY,
    modifierListState ?? new ModifierListState(),
  );
};

export const getModifierListState = () => {
  return getContext<ReturnType<typeof setModifierListState>>(MODIFIER_LIST_KEY);
};
