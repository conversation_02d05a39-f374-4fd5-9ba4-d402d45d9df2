<script lang="ts">
  import Modal from "$lib/components/Modal.svelte";
  import { formatMillisToTime } from "$lib/helpers/time";
  import { getStoreInfoState } from "$lib/services/StoreInfo.svelte";

  type Props = {
    bannerImageUrl?: string;
  };

  const storeInfo = getStoreInfoState();

  let { bannerImageUrl }: Props = $props();

  let hoursModal: ReturnType<typeof Modal> | undefined = $state(undefined);
  let headerElement: HTMLElement;

  $effect(() => {
    if (bannerImageUrl) {
      setHeaderColors({ backgroundBrightness: "dark" });
    } else {
      setHeaderColors({ backgroundBrightness: "light" });
    }
  });

  function setHeaderColors({
    backgroundBrightness,
  }: {
    backgroundBrightness: "dark" | "light";
  }) {
    const styleEl = headerElement.style;

    if (backgroundBrightness === "dark") {
      styleEl.setProperty("--merchant-name-color", "white");
      styleEl.setProperty("--merchant-address-color", "var(--gray-200)");
      styleEl.setProperty("--merchant-hours-color", "var(--gray-200)");
    }

    if (backgroundBrightness === "light") {
      styleEl.setProperty("--merchant-name-color", "black");
      styleEl.setProperty("--merchant-address-color", "var(--gray-900)");
      styleEl.setProperty("--merchant-hours-color", "var(--gray-900)");
    }
  }
</script>

{#snippet Hours()}
  <div class="scrollable-hours-content">
    {#each storeInfo.weekDayHours as { day, hours }}
      <div class="day-hours">
        <span class="day-name">{day}</span>
        <section class="hours-container">
          {#each hours as hour}
            <span class="hour-range">
              {#if hour.open && hour.close}
                {formatMillisToTime(hour.open)} - {formatMillisToTime(
                  hour.close,
                )}
              {:else}
                <span>Store Hours Not Available</span>
              {/if}
            </span>
          {/each}
        </section>
      </div>
    {/each}
  </div>
{/snippet}

<header
  bind:this={headerElement}
  style={bannerImageUrl ? `background-image: url(${bannerImageUrl})` : ""}
>
  <div
    class:dim={bannerImageUrl}
    class:white={!bannerImageUrl}
    class="overlay-wrapper"
  >
    <div class="merchant-info">
      <img class="merchant-logo" src="/merchant_logo.png" alt="Merchant Logo" />
      <div>
        <h1 class="merchant-name">{storeInfo.name}</h1>
        <p class="merchant-address">{storeInfo.formattedAddress}</p>
        {#if storeInfo.weekDayHours.length}
          <div class="merchant-hours">
            <button
              onclick={() =>
                hoursModal?.open({
                  title: "Ordering Hours",
                  children: Hours,
                })}
              class="hours-button"
            >
              Online Ordering Hours
            </button>
          </div>
        {/if}
      </div>
    </div>
  </div>
</header>

<Modal bind:this={hoursModal} />

<style>
  header {
    --merchant-name-color: black;
    --merchant-address-color: var(--gray-900);
    --merchant-hours-color: var(--gray-900);

    border-bottom: 1px solid var(--gray-300);
    background-position: center;
    background-size: cover;
    min-height: var(--min-header-height-mobile);
  }

  .overlay-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: var(--padding-md);
    height: 100%;
    min-height: 150px;

    &.dim {
      background-color: rgba(0, 0, 0, 0.6);
    }

    &.white {
      background-color: white;
    }
  }

  .merchant-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: var(--gap-sm);
    height: 100%;
    text-align: center;
  }

  .merchant-name {
    color: var(--merchant-name-color);
  }

  .merchant-logo {
    flex: 0 1 0;
    height: 100px;
  }

  .merchant-address {
    color: var(--merchant-address-color);
    font-size: var(--font-md);
  }

  .merchant-name {
    font-size: var(--font-xl);
  }

  .merchant-hours {
    display: flex;
    align-items: center;
    gap: var(--gap-sm);
    margin-top: var(--gap-sm);
    color: var(--merchant-hours-color);
    font-size: var(--font-sm);
  }

  .day-name {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    min-width: 70px;
    font-weight: bold;
  }

  .hour-range {
    display: flex;
    align-items: center;
    margin-bottom: var(--margin-xs);
    border-radius: var(--radius-md);
    background-color: var(--gray-100);
    padding: var(--padding-sm);
    width: fit-content;
  }

  .hours-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: var(--font-xs);
    letter-spacing: 1px;
  }

  .hours-button {
    transition: all 0.2s ease-in-out;
    background-color: transparent;
    font-weight: 500;
    text-decoration: underline;
  }

  .hours-button:hover {
    text-decoration-thickness: 2px;
  }

  .day-hours {
    display: grid;
    grid-template-columns: 1fr 3fr;
    gap: var(--gap-md);
    margin-bottom: var(--gap-md);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--padding-xs);

    &:last-child {
      border-bottom: none;
    }
  }

  .scrollable-hours-content {
    padding: var(--padding-xs);
    overflow-y: auto;
  }

  @media (min-width: 641px) {
    header {
      min-height: var(--min-header-height);
      max-height: 400px;
    }

    .overlay-wrapper {
      flex-direction: row;
      justify-content: space-between;
      min-height: var(--min-header-height);
    }

    .merchant-info {
      flex-direction: row;
      justify-content: flex-start;
      gap: var(--gap-md);
      text-align: left;
    }

    .merchant-name {
      font-size: var(--font-4xl);
    }

    .hours-container {
      font-size: initial;
    }

    .day-name {
      min-width: initial;
    }
  }
</style>
