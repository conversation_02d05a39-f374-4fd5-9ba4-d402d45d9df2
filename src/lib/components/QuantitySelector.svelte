<script lang="ts">
  import { centToDollar } from "$lib/helpers/number.ts";
  import MinusIcon from "~icons/mdi/minus";
  import PlusIcon from "~icons/mdi/plus";

  const min = 1;
  const MAX_QUANTITY = 10;

  let {
    max,
    showConfirm = false,
    onConfirm = () => {},
    quantity = $bindable(min),
    price,
  }: {
    max: number;
    showConfirm?: boolean;
    onConfirm?: (args: { quantity: number }) => void;
    quantity: number;
    price: number;
  } = $props();

  const vocalizeQuantitySR = () => {
    const liveRegion = document.getElementById("quantity-live");
    if (!liveRegion) return;
    liveRegion.textContent = `Current quantity: ${quantity}. Your total for this item is ${centToDollar(
      price * quantity,
    )}`;
  };

  $effect(() => {
    if (max === -1) {
      // -1 refers to unlimited quantity
      max = MAX_QUANTITY;
    }
  });
</script>

<div class="quantity-selector">
  <p class="label">Quantity</p>
  <div class="steppers-wrapper">
    <button
      class="stepper"
      disabled={quantity === min}
      onclick={() => {
        quantity = Math.max(min, quantity - 1);
        vocalizeQuantitySR();
      }}
      aria-label="Decrease quantity by 1"
    >
      <MinusIcon />
    </button>
    <span class="quantity" aria-live="polite">
      {quantity}
    </span>
    <button
      class="stepper"
      disabled={quantity === max}
      onclick={() => {
        quantity = Math.min(max === -1 ? MAX_QUANTITY : max, quantity + 1);
        vocalizeQuantitySR();
      }}
      aria-label="Increase quantity by 1"
    >
      <PlusIcon />
    </button>
  </div>
  {#if showConfirm}
    <button
      class="confirm"
      disabled={quantity === 0}
      onclick={() => {
        onConfirm({ quantity });
      }}
    >
      Confirm quantity
    </button>
  {/if}
  <div id="quantity-live" class="sr-only" aria-live="polite"></div>
</div>

<style>
  .quantity-selector {
    display: flex;
    align-items: center;
  }

  .label {
    margin-inline-end: var(--gap-md);
    color: black;
    font-weight: bold;
  }

  .steppers-wrapper {
    display: flex;
    align-items: center;
    gap: var(--gap-md);
    margin-left: 10px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
    border-radius: var(--radius-md);
    padding: var(--padding-xs);
  }

  .stepper {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: transparent;
    user-select: none;
  }

  .stepper:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .quantity {
    min-width: 20px;
    font-size: var(--font-lg);
    text-align: center;
  }

  .confirm {
    transition: ease-in-out 0.3s;
    margin-inline-start: 2rem;
    border-radius: var(--radius-md);
    background-color: var(--primary-color);
    padding: var(--padding-sm) var(--padding-md);
    color: white;
    font-weight: bold;
  }

  .confirm:hover:not(:disabled) {
    background-color: var(--primary-color-hover);
  }

  .confirm:disabled {
    cursor: not-allowed;
  }
</style>
