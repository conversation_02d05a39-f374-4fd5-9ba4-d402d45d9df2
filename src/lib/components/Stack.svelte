<script lang="ts">
  import { StackedSnippetState } from "$lib/services/StackedSnippetState.svelte.js";
  import { fly } from "svelte/transition";
  import BackIcon from "~icons/mdi/arrow-back";

  let { stackedSnippetState }: { stackedSnippetState: StackedSnippetState } =
    $props();

  const stackedSnippets = $derived(stackedSnippetState.stackedSnippets);
</script>

{#each stackedSnippets as { snippet, snippetProps, title, HeaderSnippet, headerSnippetProps }, i}
  <section
    aria-labelledby={`stack-${i}`}
    class="stacked"
    transition:fly={{ x: 200 }}
  >
    <header>
      <button
        aria-label="Go Back"
        class="go-back-button"
        onclick={() => {
          stackedSnippetState.pop();
        }}
      >
        <BackIcon />
      </button>
      {#if title}
        <h2 id={`stack-${i}`}>{title}</h2>
      {/if}
      {#if HeaderSnippet}
        <HeaderSnippet {...headerSnippetProps} />
      {/if}
    </header>

    <section>
      {@render snippet(snippetProps)}
    </section>
  </section>
{/each}

<style>
  .stacked {
    display: flex;
    position: absolute;
    flex-direction: column;
    inset: 0;
    border-radius: var(--border-radius);
    background-color: white;
  }

  .go-back-button {
    display: flex;
    border-radius: var(--radius-sm);
    padding: var(--padding-sm);
  }

  header {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: var(--gap-sm);
    column-gap: var(--gap-md);
    padding: var(--padding-md);
  }

  section {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    overflow: auto;
  }
</style>
