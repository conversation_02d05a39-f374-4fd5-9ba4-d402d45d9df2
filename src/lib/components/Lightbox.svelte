<script lang="ts">
  let { children } = $props();
</script>

<div class="lightbox-wrapper">
  <div class="lightbox">
    {@render children()}
  </div>
</div>

<div class="overlay"></div>

<style>
  .lightbox-wrapper {
    position: fixed;
    display: grid;
    place-items: center;
  }

  .lightbox {
    position: fixed;
    background-color: white;
    padding: var(--padding-md);
    box-shadow: var(--shadow-elevation-high);
  }

  .overlay {
    position: fixed;
    inset: 0;
    background-color: black;
    opacity: 50%;
  }
</style>
