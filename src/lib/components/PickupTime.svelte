<script module lang="ts">
  import { logger } from "$lib/logger/logger.svelte";

  export const initPickupTime = () => {
    const date = new Date(new Date().setMinutes(new Date().getMinutes()));
    const emailDate = new Date(
      new Date().setMinutes(new Date().getMinutes() + 30),
    );

    const dateFormatted = Number(
      date
        .toLocaleTimeString("en-US", {
          hour: "2-digit",
          minute: "2-digit",
          hour12: false,
        })
        .replace(":", ""),
    );

    if (!Number.isFinite(dateFormatted)) {
      const errorMessage = `Failed to parse number from date for bridge service: ${dateFormatted}`;
      logger.error({ dateFormatted, date }, errorMessage);
      throw new Error(errorMessage);
    }

    return {
      emailFormat: emailDate.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      }),
      inputFormat: date.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: false,
      }),
      value: dateFormatted,
      isAsap: true,
    };
  };
</script>

<script lang="ts">
  import { toast } from "$lib/services/ToastManager.svelte";
  import { slide } from "svelte/transition";
  import CalendarIcon from "~icons/mdi/calendar-clock";
  import ClockIcon from "~icons/mdi/clock-outline";

  let {
    pickupTime = $bindable(),
  }: {
    pickupTime: {
      emailFormat: string;
      inputFormat: string;
      value: number;
      isAsap: boolean;
    };
  } = $props();

  // TODO: Check restaurant's working hours
  const handleCustomTimeChange = (e: Event) => {
    const timeValue = (e.currentTarget as HTMLInputElement).value;
    const timeDate = new Date();
    const [hours, minutes] = timeValue.split(":").map(Number);

    if (
      typeof hours !== "number" ||
      typeof minutes !== "number" ||
      Number.isNaN(hours) ||
      Number.isNaN(minutes)
    ) {
      return;
    }

    const selectedTime = new Date().setHours(hours, minutes);
    if (selectedTime < Date.now()) {
      toast.warning("Pickup time must be in the future");
      pickupTime = initPickupTime();
      return;
    }

    timeDate.setHours(hours, minutes);

    pickupTime = {
      emailFormat: timeDate.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      }),
      inputFormat: timeValue,
      value: Number(timeValue.replace(":", "")),
      isAsap: false,
    };
  };
</script>

<div class="pickup-time-wrapper">
  <label for="pickup-time">Pickup Time:</label>

  <div class="pickup-options">
    <button
      class="option-button"
      class:selected={pickupTime.isAsap}
      onclick={() => (pickupTime.isAsap = true)}
    >
      <ClockIcon />
      <div class="button-content">
        <span class="button-title">ASAP</span>
        <span class="button-subtitle">{pickupTime.emailFormat}</span>
      </div>
    </button>

    <button
      class="option-button"
      class:selected={!pickupTime.isAsap}
      onclick={() => (pickupTime.isAsap = false)}
      aria-pressed={!pickupTime.isAsap}
    >
      <CalendarIcon />
      <div class="button-content">
        <span class="button-title">Choose Time</span>
      </div>
    </button>
  </div>

  {#if !pickupTime.isAsap}
    <div class="time-picker-container" transition:slide>
      <input
        id="pickup-time"
        type="time"
        aria-label="Pickup Time"
        value={pickupTime.inputFormat}
        onchange={handleCustomTimeChange}
      />
      <p class="time-note">Please select a time for pickup</p>
    </div>
  {:else}
    <p class="time-note">
      Your order will be ready in approximately 30 minutes
    </p>
  {/if}
</div>

<style>
  .pickup-time-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    width: 100%;
  }

  .pickup-options {
    display: flex;
    gap: var(--gap-sm);
    margin-bottom: var(--margin-sm);
    width: 100%;
  }

  .option-button {
    display: flex;
    flex: 1;
    align-items: center;
    gap: var(--gap-sm);
    transition: all 0.2s ease;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    background-color: white;
    padding: var(--padding-sm);
  }

  .option-button.selected {
    border-color: var(--primary-color);
    background-color: var(--primary-color-light);
  }

  .option-button:hover {
    border-color: var(--primary-color-hover);
  }

  .button-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .button-title {
    font-weight: 600;
    font-size: var(--font-sm);
  }

  .button-subtitle {
    color: var(--gray-700);
    font-size: var(--font-xs);
  }

  .time-picker-container {
    width: 100%;
  }

  .time-note {
    margin-top: var(--margin-xs);
    color: var(--gray-700);
    font-size: var(--font-xs);
  }

  input[type="time"] {
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    padding: var(--padding-sm);
    width: 100%;
  }

  :global(.option-button svg) {
    color: var(--primary-color);
    font-size: var(--font-lg);
  }
</style>
