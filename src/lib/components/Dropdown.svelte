<script lang="ts">
  import { page } from "$app/stores";
  import { createDropdownMenu, melt } from "@melt-ui/svelte";
  import { quintOut } from "svelte/easing";
  import { slide } from "svelte/transition";
  import ListIcon from "~icons/mdi/format-list-bulleted";
  import type { BasicDepartment } from "$lib/types";

  interface Props {
    items: BasicDepartment[];
    onItemSelect?: (item: BasicDepartment) => void;
  }

  let { items }: Props = $props();

  const {
    elements: { menu, item, trigger },
    states: { open: isOpen },
  } = createDropdownMenu({
    closeFocus: () => {
      const anchorElementId = $page.url.toString().split("#").pop();
      return document.querySelector(`#${anchorElementId}`) as HTMLElement;
    },
    loop: true,
  });
</script>

<div class="dropdown-container">
  <button use:melt={$trigger} class="icon">
    <ListIcon font-size="var(--font-md)" />
    <span class="sr-only">Dropdown</span>
  </button>
  {#if $isOpen}
    <div
      use:melt={$menu}
      class="menu"
      transition:slide={{ duration: 300, easing: quintOut }}
    >
      <div class="menu-items" aria-orientation="vertical">
        <ul>
          {#each items as itemEntry}
            <li class="menu-item">
              <a
                onm-click={(e) => {
                  e.preventDefault();
                  $isOpen = false;
                }}
                href="#{itemEntry.title.toLowerCase().replace(/ /g, '-')}"
                class="item-button"
                use:melt={$item}
              >
                {itemEntry.title}
              </a>
            </li>
          {/each}
        </ul>
      </div>
    </div>
  {/if}
</div>

<style>
  .dropdown-container {
    position: relative;
  }

  .menu {
    position: absolute;
    margin-top: var(--margin-xs);
    box-shadow: var(--shadow-elevation-high);
    border-radius: var(--radius-md);
    background-color: white;
    width: fit-content;
    max-height: 70vh;
    overflow: auto;
  }

  .menu-items {
    padding: var(--padding-md);
  }

  ul {
    margin: 0;
    padding: 0;
  }

  .menu-item {
    list-style: none;
  }

  li:not(:first-child) {
    border-top: 1px solid var(--gray-200);
  }

  .item-button {
    display: block;
    transition: background-color 0.3s;
    padding: var(--padding-md);
    color: var(--gray-800);
    font-size: var(--font-sm);
    text-decoration: none;

    &:focus {
      outline: var(--primary-color) auto 2px;
    }
  }

  .icon {
    border: none;
    border-radius: var(--radius-full);
    background: none;
    padding: var(--padding-sm);
  }

  .icon:hover {
    background-color: var(--gray-100);
  }

  .icon:focus {
    outline: var(--primary-color) auto 2px;
  }
</style>
