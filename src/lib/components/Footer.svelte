<footer>
  <div class="section">
    <p>Powered by</p>
    <img src="/Round2POS.png" alt="Round 2 POS" class="company-logo" />
  </div>
  <div class="section">
    <!--TODO: Implement Actual Links to Privacy Policy && Terms of Use and change these buttons to anchor elements-->
    <button class="footer-button">Privacy Policy</button>
    <button class="footer-button">Terms of Use</button>
    <span class="corporate-tag">
      &copy; {new Date().getFullYear()} Round 2 POS, LLC
    </span>
  </div>
</footer>

<style>
  footer {
    background-color: var(--main-bg-color);
    padding: var(--padding-md);
    border-top: 1px solid var(--gray-300);
    height: var(--footer-height);
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-top: auto;
    transition: width 0.3s ease;
  }
  .section p {
    margin: 0;
    font-size: var(--font-xs);
    color: var(--gray-800);
  }

  .section {
    display: flex;
    align-items: center;
  }
  .section + .section {
    gap: 10px;
  }
  .company-logo {
    height: 40px;
    width: auto;
    margin-left: var(--padding-sm);
  }
  .corporate-tag {
    font-size: var(--font-size-sm);
    font-weight: bold;
    color: var(--gray-900);
  }

  .footer-button {
    background-color: transparent;
    font-size: var(--font-size-sm);
    color: var(--gray-900);
    text-decoration: none;
    transition:
      background-color 0.3s,
      color 0.3s;
  }

  .footer-button:hover {
    text-decoration: underline;
  }

  @media (max-width: 768px) {
    footer {
      flex-direction: column;
      gap: var(--padding-md);
      font-size: var(--font-xs);
    }
    .section {
      margin-bottom: var(--padding-md);
    }
  }
  @media (min-width: 1025px) {
    :global(body:has(.large-screen-cart)) footer {
      width: calc(100% - var(--large-screen-cart-width));
    }
  }
</style>
