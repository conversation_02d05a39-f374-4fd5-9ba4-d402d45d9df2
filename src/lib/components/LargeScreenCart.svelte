<script lang="ts">
  import Cart from "$lib/components/Cart.svelte";
  import { getCartState } from "$lib/services/Cart.svelte.ts";
  import { onMount } from "svelte";
  import EmptyCartIcon from "~icons/mdi/cart-remove";

  let cartElement: HTMLElement;
  let isHeaderShowing = true;

  const cart = getCartState();

  const initializeHeaderObserver = () => {
    const header = document.querySelector("header");
    if (!header) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        isHeaderShowing = Boolean(entry && entry.isIntersecting);
        if (cartElement) {
          cartElement.style.top = isHeaderShowing
            ? `calc(var(--min-header-height) + 1px)`
            : "0";
        }
      },
      {
        threshold: 0,
        rootMargin: `-${header.offsetHeight}px 0px 0px 0px`,
      },
    );

    observer.observe(header);
    return () => observer.disconnect();
  };

  $effect(() => {
    if (cart.totalItemsCount) {
      cart.vocalizeUpdateForSR();
    }
  });

  onMount(() => {
    initializeHeaderObserver();
  });
</script>

<div class="large-screen-cart" bind:this={cartElement}>
  {#if cart.totalItemsCount > 0}
    <Cart />
  {:else}
    <div class="empty-cart">
      <div class="cart-icon">
        <EmptyCartIcon
          font-size="var(--font-2xl)"
          color="var(--primary-color)"
          width="50%"
          height="50%"
        />
      </div>
      <p>Your cart is empty</p>
    </div>
  {/if}
</div>
<div id="quantity-live-region" class="sr-only" aria-live="polite"></div>

<style>
  .large-screen-cart {
    position: fixed;
    right: 0;
    width: var(--large-screen-cart-width);
    height: 100vh;
    background-color: white;
    border-left: 1px solid var(--gray-200);
    overflow-y: hidden;
    transition: top 0.3s ease;

    @media (max-width: 1024px) {
      display: none;
    }

    @media (prefers-reduced-motion) {
      transition: none;
    }
  }

  .empty-cart {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
  }

  .cart-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }

  p {
    font-size: var(--font-lg);
    margin: 0;
    font-weight: bold;
  }
</style>
