<script lang="ts">
  import { getHeaderState } from "$lib/services/TopHeader.svelte.ts";

  const TOP_HEADER_BUFFER = 10;

  let {
    name,
    stickyHeaderHeight,
  }: { name: string; stickyHeaderHeight: number } = $props();

  let headerElement: HTMLElement;
  let isTopHeader = $state(false);

  const topHeader = getHeaderState();

  const checkIfTopHeader = () => {
    if (headerElement && typeof stickyHeaderHeight !== "undefined") {
      const rect = headerElement.getBoundingClientRect();
      isTopHeader =
        rect.top <= stickyHeaderHeight + TOP_HEADER_BUFFER && rect.bottom > 0;
      if (isTopHeader) {
        topHeader.setTopHeader(name ?? "");
      }
    }
  };
</script>

<svelte:window onscroll={checkIfTopHeader} />

<h2
  bind:this={headerElement}
  id={name?.toLowerCase().replace(/ /g, "-")}
  class="department-header"
  tabindex="-1"
>
  {name}
</h2>

<style>
  .department-header {
    padding-inline: var(--padding-sm);
    scroll-margin-top: var(--sticky-header-height);
    color: var(--gray-950);
    font-weight: bold;
    font-size: var(--font-xl);
    line-height: var(--line-height-lg);
    text-decoration: none;
  }
</style>
