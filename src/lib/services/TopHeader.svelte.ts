import { getContext, onDestroy, setContext } from "svelte";

const HEADER_KEY = Symbol("header");

export class TopHeader {
  private topHeader = $state<string | null>(null);

  constructor() {
    onDestroy(() => {
      this.topHeader = null;
    });
  }

  public setTopHeader(header: string | null) {
    this.topHeader = header;
  }

  public getTopHeader() {
    return this.topHeader;
  }
}

export const initHeaderState = () => {
  return setContext(HEADER_KEY, new TopHeader());
};

export const getHeaderState = () => {
  return getContext<TopHeader>(HEADER_KEY);
};
