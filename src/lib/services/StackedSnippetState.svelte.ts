import { type Component, getContext, setContext, type Snippet } from "svelte";

type StackedSnippet<T, K extends Record<string, unknown>> = {
  snippet: Snippet<[T]>;
  snippetProps: T;
  title?: string;
  HeaderSnippet?: Component<K>;
  headerSnippetProps?: K;
};

export class StackedSnippetState {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private _stackedSnippets: Array<StackedSnippet<any, any>> = $state([]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public get stackedSnippets(): ReadonlyArray<StackedSnippet<any, any>> {
    return this._stackedSnippets;
  }

  public push = <T, K extends Record<string, unknown>>({
    snippet,
    snippetProps,
    title,
    HeaderSnippet,
    headerSnippetProps,
  }: StackedSnippet<T, K>) => {
    this._stackedSnippets.push({
      snippet,
      snippetProps,
      title,
      HeaderSnippet,
      headerSnippetProps,
    });
  };

  public pop = () => {
    this._stackedSnippets.pop();
  };

  public reset = () => {
    this._stackedSnippets = [];
  };
}

const STACKED_SNIPPET_KEY = Symbol("stackedSnippet");

export const initStackedSnippetState = () => {
  const newState = new StackedSnippetState();
  return setContext(STACKED_SNIPPET_KEY, newState);
};

// TODO Will this cause data leaking issues across merchants?
// TODO Should be moved to standard get/set context pattern?
export const getStackedSnippetState = (): ReturnType<
  typeof initStackedSnippetState
> => {
  return getContext(STACKED_SNIPPET_KEY);
};
