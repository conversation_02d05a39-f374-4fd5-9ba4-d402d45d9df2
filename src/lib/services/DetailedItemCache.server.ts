import type { DetailedItem } from "$lib/types";
import { redis } from "$lib/server/redis.server.ts";
import { logger } from "$lib/logger/logger.svelte.ts";

const FIVE_MINUTES_IN_SECONDS = 300;

/**
 * @param ttlSeconds - Time to live in seconds for cached items (default: 300 = 5 minutes)
 * @param enabled - Whether caching is enabled (default: true)
 * @param keyPrefix - Key prefix for storage keys (default: "detailed_item")
 */
const DEFAULT_CONFIG = {
  ttlSeconds: FIVE_MINUTES_IN_SECONDS,
  enabled: true,
  keyPrefix: "detailed_item",
};

export class DetailedItemCache {
  private config: typeof DEFAULT_CONFIG;

  constructor(config: Partial<typeof DEFAULT_CONFIG> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  private getCacheKey(restaurantName: string, itemId: string): string {
    return `${this.config.keyPrefix}:${restaurantName}:${itemId}`;
  }

  async get(
    restaurantName: string,
    itemId: string,
  ): Promise<DetailedItem | null> {
    logger.trace(
      { restaurantName, itemId },
      "Getting detailed item from cache...",
    );

    if (!this.config.enabled) {
      logger.trace(
        { restaurantName, itemId },
        "Caching is disabled, skipping cache get",
      );
      return null;
    }

    try {
      const key = this.getCacheKey(restaurantName, itemId);
      const cachedData = await redis.get(key);

      if (!cachedData) {
        logger.trace(
          { restaurantName, itemId },
          "Cache miss for detailed item",
        );
        return null;
      }

      const detailedItem = JSON.parse(cachedData) as DetailedItem;
      logger.trace(
        { restaurantName, itemId },
        "Cache hit for detailed item. Returning cached data...",
      );

      return detailedItem;
    } catch (error) {
      logger.warn(
        { restaurantName, itemId, error },
        "Failed to get detailed item from cache, falling back to bridge API",
      );
      return null;
    }
  }

  async set(
    restaurantName: string,
    itemId: string,
    detailedItem: DetailedItem,
  ): Promise<void> {
    logger.trace(
      { restaurantName, itemId },
      "Setting detailed item in cache...",
    );

    if (!this.config.enabled) {
      logger.trace(
        { restaurantName, itemId },
        "Caching is disabled, skipping cache set",
      );
      return;
    }

    try {
      const key = this.getCacheKey(restaurantName, itemId);
      const serializedData = JSON.stringify(detailedItem);

      await redis.setex(key, this.config.ttlSeconds, serializedData);

      logger.trace(
        { restaurantName, itemId, ttl: this.config.ttlSeconds },
        "Cached detailed item",
      );
    } catch (error) {
      logger.warn(
        { restaurantName, itemId, error },
        "Failed to cache detailed item, continuing without cache",
      );
    }
  }

  async clear({ restaurantName }: { restaurantName: string }) {
    const keys = await redis.keys(
      `${this.config.keyPrefix}:${restaurantName}:*`,
    );
    await redis.del(...keys);
  }
}

export const detailedItemCache = new DetailedItemCache();
