type ToastType = "success" | "error" | "warning" | "info";

type ToastPosition =
  | "top-l"
  | "top-r"
  | "bottom-l"
  | "bottom-r"
  | "center-t"
  | "center-b";

type DismissableToastOptions = {
  dismissible: true;
  title?: string;
  position?: ToastPosition;
  onClose?: () => void;
};

type NonDismissableToastOptions = {
  dismissible?: false;
  title?: string;
  position?: ToastPosition;
  duration?: number;
  onClose?: () => void;
};

type ToastOptions = DismissableToastOptions | NonDismissableToastOptions;

export interface Toast {
  id: string;
  message: string;
  type: ToastType;
  title: string;
  duration: number;
  dismissible: boolean;
  position: ToastPosition;
  onClose?: () => void;
  focused: boolean;
}

export class ToastManager {
  private toasts: Toast[] = $state([]);

  private generateId(): string {
    return Math.random().toString(36).substring(2, 9);
  }
  public alert(
    message: string,
    type: ToastType = "info",
    options: ToastOptions = {},
  ) {
    const id = this.generateId();
    const dismissible = options.dismissible ?? false;
    const duration = "duration" in options ? options.duration ?? 5000 : 5000;

    const toast: Toast = {
      id,
      message,
      type,
      title: options.title ?? "",
      duration,
      dismissible,
      position: options.position ?? "center-t",
      onClose: options.onClose,
      focused: options.dismissible ?? false,
    };

    this.toasts.push(toast);

    if (!dismissible && toast.duration > 0) {
      setTimeout(() => {
        this.removeToast(id);
      }, toast.duration);
    }
  }

  public success(message: string, options?: ToastOptions) {
    this.alert(message, "success", options);
  }

  public error(message: string, options?: ToastOptions) {
    this.alert(message, "error", options);
  }

  public warning(message: string, options?: ToastOptions) {
    this.alert(message, "warning", options);
  }

  public info(message: string, options?: ToastOptions) {
    this.alert(message, "info", options);
  }

  public removeToast(id: string) {
    const toastIndex = this.toasts.findIndex((t) => t.id === id);
    const toast = this.toasts[toastIndex];
    if (toast?.onClose) {
      toast.onClose();
    }

    this.toasts.splice(toastIndex, 1);
    // Find the next dismissible toast and set focus to true
    for (const toast of this.toasts) {
      if (toast.dismissible) {
        toast.focused = true;

        const buttonToFocus = document.querySelector(
          `.close-button[data-toast-id="${toast.id}"]`,
        ) as HTMLButtonElement;

        if (buttonToFocus) {
          buttonToFocus.focus();
        }
        break;
      }
    }
  }

  public getToasts() {
    return this.toasts;
  }
}

export const toast = new ToastManager();
