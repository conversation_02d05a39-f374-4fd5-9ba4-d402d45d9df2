import { CheckoutError, type CheckoutErrorName } from "./CheckoutError";
import { toast } from "$lib/services/ToastManager.svelte";

const ERROR_MESSAGES: Record<CheckoutErrorName, string> = {
  UNKNOWN: "An unexpected error occurred. Please try again.",
  PAYMENT_PROCESSING:
    "Payment processing failed. Please try again or use a different payment method.",
  BRIDGE_DATA_FORWARDING:
    "Unable to send your order to the restaurant. Please try again.",
  CHECKOUT_FINALIZATION:
    "Your payment was processed, but we couldn't complete your order. Please contact support.",
  VALIDATION: "Please check your information and try again.",
  NETWORK: "Network error. Please check your connection and try again.",
  FIRST_NAME_REQUIRED: "Please enter your first name.",
  LAST_NAME_REQUIRED: "Please enter your last name.",
  EMAIL_REQUIRED: "Please enter your email.",
  PHONE_REQUIRED: "Please enter your phone number.",
  CARD_NUMBER_REQUIRED: "Please enter your card number.",
  GET_TOTALS:
    "Unable to reach our servers. Please try again later or contact support.",
  TOTALS_DO_NOT_MATCH:
    "Unable to reach our servers. Please try again later or contact support.",
  SALE_BLOCKED:
    "The items in your order are not available at this time. Please contact support or choose different items.",
  HEALTH_CHECK_FAILED:
    "Unable to reach our servers. Please try again later or contact support.",
  RESTAURANT_OFFLINE:
    "The restaurant is currently offline. Please try again later or contact support.",
  EMAIL_SENDING_ERROR:
    "The order was processed, but we couldn't send the receipt. Please contact support.",
};

export class ErrorHandler {
  static sendUserFriendlyError(error: unknown) {
    if (!(error instanceof Error)) {
      toast.error("An unexpected error occurred");
      return;
    }

    if (!error.name || !(error instanceof CheckoutError)) {
      console.error(error.message);
      toast.error("An unexpected error occurred");
      return;
    }

    const checkoutError = error;
    const errorName = checkoutError.name;
    const errorMessage =
      ERROR_MESSAGES[errorName as CheckoutErrorName] || ERROR_MESSAGES.UNKNOWN;
    toast.error(errorMessage);
  }
}
