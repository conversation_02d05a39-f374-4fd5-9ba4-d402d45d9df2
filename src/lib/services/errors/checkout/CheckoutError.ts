export type CheckoutErrorName =
  | "CHECKOUT_FINALIZATION"
  | "PAYMENT_PROCESSING"
  | "BRIDGE_DATA_FORWARDING"
  | "VALIDATION"
  | "NETWORK"
  | "UNKNOWN"
  | "FIRST_NAME_REQUIRED"
  | "LAST_NAME_REQUIRED"
  | "EMAIL_REQUIRED"
  | "PHONE_REQUIRED"
  | "CARD_NUMBER_REQUIRED"
  | "GET_TOTALS"
  | "TOTALS_DO_NOT_MATCH"
  | "SALE_BLOCKED"
  | "HEALTH_CHECK_FAILED"
  | "RESTAURANT_OFFLINE"
  | "EMAIL_SENDING_ERROR";

export class CheckoutError extends Error {
  name: CheckoutErrorName;
  message: string;
  cause: unknown;

  constructor({
    name,
    message,
    cause,
  }: {
    name: CheckoutErrorName;
    message: string;
    cause?: unknown;
  }) {
    super();
    this.name = name;
    this.message = message;
    this.cause = cause;

    this.logError();
  }

  logError(): void {
    console.error(`CheckoutError: [${this.name}]: ${this.message}`, {
      cause: this.cause,
    });
  }
}
