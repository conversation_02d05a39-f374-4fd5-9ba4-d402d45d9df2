import { browser } from "$app/environment";
import type { IBridgeClient } from "$lib/client/bridge/bridge-client.ts";
import { getCurrentDayKey, isClosingSoon } from "$lib/helpers/store-info";
import {
  formatMillisToHoursAndMinutes,
  formatMillisToMinutes,
} from "$lib/helpers/time";
import { getStoreInfoState } from "$lib/services/StoreInfo.svelte.ts";
import { toast } from "$lib/services/ToastManager.svelte.js";
import { getContext, setContext } from "svelte";

const TWO_HOURS_IN_MS = 2 * 60 * 60 * 1000;
const FIFTEEN_MINUTES_IN_MS = 15 * 60 * 1000;
const ONE_MINUTE_IN_MS = 60 * 1000;

export class HealthCheckService {
  private readonly checkIntervalMs: number;
  private readonly bridgeClient: IBridgeClient;
  private isOnline: boolean = $state(true);
  private isClosingSoon: boolean = $state(false);
  private timeUntilClosingInMs: number | undefined = $state(undefined);
  private intervalId?: NodeJS.Timeout;
  private storeInfo = getStoreInfoState();
  private hasShownClosingSoonToast: boolean = $state(false);

  constructor({
    bridgeClient,
    checkIntervalMs = 30000,
  }: {
    bridgeClient: IBridgeClient;
    checkIntervalMs?: number;
  }) {
    this.bridgeClient = bridgeClient;
    this.checkIntervalMs = checkIntervalMs;
  }

  public async startPolling(): Promise<void> {
    if (!browser) {
      return;
    }

    await this.checkHealth();

    if (this.intervalId) {
      clearInterval(this.intervalId);
    }

    this.intervalId = setInterval(() => {
      this.checkHealth();
    }, this.checkIntervalMs);

    await this.checkIfClosingSoon();

    setInterval(() => {
      this.checkIfClosingSoon();
    }, this.isClosingSoon ? ONE_MINUTE_IN_MS : FIFTEEN_MINUTES_IN_MS);
  }

  public stopPolling() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }

  get online() {
    return this.isOnline;
  }

  get closingSoon() {
    return this.isClosingSoon;
  }

  get timeUntilClosing() {
    return this.timeUntilClosingInMs;
  }

  private async checkHealth(): Promise<void> {
    try {
      const healthCheck = await this.bridgeClient.getHealth();
      const isOnline = healthCheck.ok;

      if (this.isOnline !== isOnline) {
        if (isOnline) {
          toast.success("Restaurant is now online and accepting orders", {
            duration: 5000,
          });
        } else {
          toast.error("Restaurant has gone offline and cannot process orders", {
            duration: 5000,
          });
        }
      }

      this.isOnline = isOnline;
    } catch (error) {
      console.error("Health check failed:", error);
      toast.error("Unable to connect to restaurant servers");
      this.isOnline = false;
    }
  }

  private async checkIfClosingSoon() {
    const currentDayKey = getCurrentDayKey();
    const hoursForToday = currentDayKey
      ? this.storeInfo.storeHours[currentDayKey]
      : undefined;

    if (!hoursForToday) {
      return;
    }

    const { closingSoon, timeUntilClosingInMs, timeUntilOpeningInMs } =
      isClosingSoon({ hoursForToday });

    const { hours: hoursUntilOpening, minutes: minutesUntilOpening } =
      formatMillisToHoursAndMinutes(timeUntilOpeningInMs ?? 0);

    const hoursString = (() => {
      if (hoursUntilOpening < 1) {
        return "";
      } else if (hoursUntilOpening === 1) {
        return "1 hour";
      } else {
        return `${hoursUntilOpening} hours`;
      }
    })();
    const minutesString = (() => {
      if (minutesUntilOpening < 1) {
        return "";
      } else if (minutesUntilOpening === 1) {
        return "1 minute";
      } else {
        return `${minutesUntilOpening} minutes`;
      }
    })();

    if (timeUntilOpeningInMs) {
      this.setOffline();
      if (timeUntilOpeningInMs < TWO_HOURS_IN_MS) { 
      toast.warning(
          `Online ordering will be available in ${hoursString ? `${hoursString} and ` : ""}${minutesString ? `${minutesString}` : ""}!`,
          { duration: 5000 },
        );
      }
    }

    if (closingSoon) {
      this.isClosingSoon = true;
      this.timeUntilClosingInMs = timeUntilClosingInMs;
      if (!this.hasShownClosingSoonToast && this.timeUntilClosingInMs) {
        toast.warning(
          `Online ordering will be unavailable in ${formatMillisToMinutes(
            this.timeUntilClosingInMs,
          )} ${this.timeUntilClosingInMs < 60000 ? "minute" : "minutes"}!`,
          { duration: 5000 },
        );
        this.hasShownClosingSoonToast = true;
      }
    } else if (!closingSoon && this.hasShownClosingSoonToast) {
      this.hasShownClosingSoonToast = false;
      this.isClosingSoon = false;
      this.timeUntilClosingInMs = undefined;
    }
  }

  private setOffline() {
    this.stopPolling();
    this.isOnline = false;
  }
}

const HEALTH_CHECK_KEY = Symbol("healthCheck");

export const setHealthCheckService = (
  bridgeClient: IBridgeClient,
  checkIntervalMs?: number,
) => {
  return setContext(
    HEALTH_CHECK_KEY,
    new HealthCheckService({ bridgeClient, checkIntervalMs }),
  );
};

export const getHealthCheckService = () => {
  return getContext<ReturnType<typeof setHealthCheckService>>(HEALTH_CHECK_KEY);
};
