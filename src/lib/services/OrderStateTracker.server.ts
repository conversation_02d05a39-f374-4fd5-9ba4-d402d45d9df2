import { redis } from "$lib/server/redis.server.ts";
import type { OrderInfo } from "$lib/types";
import crypto from "node:crypto";
import { logger } from "$lib/logger/logger.svelte.ts";
import { z } from "zod/v4";

export enum OrderState {
  /**
   * The default state. Set to this when the order state tracker is initialized
   */
  PENDING,
  /**
   * The function to submit the payment ran successfully. No word on response yet.
   */
  PAYMENT_SUBMITTED,
  /**
   * We received a successfully parsed response from NMI where response does not equal 1 (1 means success)
   */
  PAYMENT_FAILED,
  /**
   * We received a response with a status of 1 from NMI, meaning the payment was successful.
   */
  PAYMENT_SUCCESS,
  ORDER_SUBMITTED,
  /**
   * We received a `!body.ok` from the bridge submit order endpoint
   */
  ORDER_FAILED,
  /**
   * We received a `body.ok` from the bridge submit order endpoint
   */
  ORDER_RECEIVED,
  /**
   * Order was successfully submitted, and email receipt function successfully ran. Should be mostly representative of whether the end user actually saw a 'transaction complete' splash.A
   */
  COMPLETED,
}

const transactionStateSchema = z.enum(OrderState);

const cachedOrderInfoSchema = z.object({
  state: transactionStateSchema,
  timestamp: z.iso.datetime(),
});

type CachedOrderInfo = z.infer<typeof cachedOrderInfoSchema>;

export class OrderStateTracker {
  private readonly ORDER_CACHE_EXPIRY_SECONDS = 86400; // 24 hours expiry

  private _idempotencyKey: string;

  state: OrderState;

  static async instance(orderInfo: OrderInfo) {
    const orderStateTracker = new OrderStateTracker();

    orderStateTracker.generateIdempotencyKey(orderInfo);

    await orderStateTracker.updateState(OrderState.PENDING);

    return orderStateTracker;
  }

  private constructor() {
    this._idempotencyKey = "";
    this.state = OrderState.PENDING;
  }

  get idempotencyKey() {
    return this._idempotencyKey;
  }

  /**
   * Generates a key to uniquely identify a transaction. This logic isn't perfect but it should be good enough for the moment.
   */
  generateIdempotencyKey(orderInfo: OrderInfo) {
    // TODO The safest way to track transactions would probably be to have an order id
    const hash = crypto
      .createHash("sha256")
      .update(
        JSON.stringify({
          cartItems: orderInfo.cart.items,
          cardLastFour: orderInfo.card.pan,
          customerPhone: orderInfo.customer.phone,
          customerName: `${orderInfo.customer.firstName} ${orderInfo.customer.lastName}`,
        }),
      )
      .digest("hex");

    this._idempotencyKey = `nmi-payment-${hash}`;
  }

  private getRedisOrderKey(idempotencyKey: string) {
    return `order-${idempotencyKey}`;
  }

  async getTransactionState(): Promise<{ state: OrderState } | null> {
    try {
      const record = await redis.get(
        this.getRedisOrderKey(this._idempotencyKey),
      );
      return record ? JSON.parse(record) : null;
    } catch (err) {
      logger.error(
        { idempotencyKey: this._idempotencyKey, error: err },
        "Failed to get transaction state from Redis",
      );
      return null;
    }
  }

  /**
   *
   * @param state
   * @param requireLinearity If true, will skip any update that is not sequential. Defaults to true
   */
  async updateState(state: OrderState, requireLinearity = true) {
    const record: CachedOrderInfo = {
      state,
      timestamp: new Date().toISOString(),
    };

    const existingRecord = await this.getTransactionState();
    const initialOrderState = existingRecord?.state ?? OrderState.PENDING;

    if (requireLinearity && state < initialOrderState) {
      logger.error(
        { idempotencyKey: this._idempotencyKey, state, initialOrderState },
        "State update is not sequential",
      );
      return;
    }

    try {
      await redis.setex(
        this.getRedisOrderKey(this._idempotencyKey),
        JSON.stringify(cachedOrderInfoSchema.parse(record)),
        this.ORDER_CACHE_EXPIRY_SECONDS,
      );

      this.state = state;
    } catch (err) {
      logger.error(
        { idempotencyKey: this._idempotencyKey, state, error: err },
        "Failed to update transaction state in Redis",
      );
    }
  }
}
