import type { ModifierListState } from "$lib/components/Modifier/modifier-list-state.svelte";
import { calculateTotalItemPrice } from "$lib/helpers/calculate-total-item-price";
import { genUuid } from "$lib/helpers/uuid";
import { centToDollar } from "$lib/helpers/number";
import { getContext, setContext } from "svelte";
import type { CartItem, DetailedItem } from "../types";
import { getStoreInfoState } from "./StoreInfo.svelte";
import { parseJSON } from "$lib/helpers/parse-json";

const defaultCartKey = genUuid();

const getCartKey = () => {
  // TODO Can we pull this from the URL (unique store name) instead of potentially defaulting to a random uuid?
  const storeName = getStoreInfoState().name ?? defaultCartKey;
  return `cart-${storeName}`;
};

export class CartState {
  public cartItems = $state<CartItem[]>([]);

  private _cartKey: string;

  constructor() {
    this._cartKey = getCartKey();

    if (typeof window !== "undefined") {
      const storedCart = localStorage.getItem(this._cartKey);
      if (storedCart) {
        this.cartItems = parseJSON(storedCart);
      }
      window.addEventListener("storage", this.handleStorageChange);

      $effect(() => {
        localStorage.setItem(this._cartKey, JSON.stringify(this.cartItems));
      });
    }
  }

  private handleStorageChange = (event: StorageEvent) => {
    if (event.key === this._cartKey) {
      const newItem = event.newValue;
      if (newItem) {
        this.cartItems = parseJSON(newItem);
      } else {
        this.cartItems = [];
      }
    }
  };

  get cartKey() {
    return this._cartKey;
  }

  public vocalizeUpdateForSR() {
    const liveRegion = document.getElementById("quantity-live-region");
    if (!liveRegion) return;

    const itemsList = this.cartItems
      .map((item) => {
        return `${item.qty} ${item.qty > 1 ? "counts" : "count"} of ${item.desc}`;
      })
      .join(", ");

    liveRegion.textContent = `Cart updated. You have 
			${itemsList}.
			This amounts to a total of ${this.totalItemsCount} items for a subtotal of ${centToDollar(this.subTotal)}`;
  }

  add(item: DetailedItem, modifierListState: ModifierListState) {
    const cartItem: CartItem = {
      ...$state.snapshot(item),
      selected: true,
      cartItemId: genUuid(),
      modifierListState: {
        modifierInputs: $state.snapshot(modifierListState.modifierInputs),
        validationStates: $state.snapshot(modifierListState.validationStates),
      },
    };

    this.cartItems.push(cartItem);
  }

  updateModifierListState(
    cartItemId: string,
    modifierListState: ModifierListState,
  ) {
    this.cartItems = this.cartItems.map((item) => {
      if (item.cartItemId === cartItemId) {
        return {
          ...item,
          modifierListState: {
            modifierInputs: $state.snapshot(modifierListState?.modifierInputs),
            validationStates: $state.snapshot(
              modifierListState?.validationStates,
            ),
          },
        };
      }
      return item;
    });

    return this.cartItems;
  }

  updateQuantity(cartItemId: string, newQuantity: number) {
    this.cartItems = this.cartItems.map((item) => {
      if (item.cartItemId === cartItemId) {
        return { ...item, qty: newQuantity };
      }
      return item;
    });
  }

  remove(cartItemId: string) {
    this.cartItems = this.cartItems.filter(
      (cartItem) => cartItem.cartItemId !== cartItemId,
    );
  }

  clear() {
    this.cartItems = [];
  }

  get subTotal(): number {
    const subTotal = this.cartItems.reduce((acc, item: CartItem) => {
      return acc + calculateTotalItemPrice(item);
    }, 0);
    return subTotal;
  }

  get items() {
    return this.cartItems;
  }

  /**
   * Gets cart items without client/browser-specific properties
   */
  get checkoutItems() {
    return this.cartItems.map((item) => {
      if ("modifierListState" in item || "cartItemId" in item) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { modifierListState, cartItemId, ...strippedItem } = item;
        return strippedItem;
      }
      return item;
    });
  }

  updateCartItem(item: CartItem) {
    this.cartItems = this.cartItems.map((cartItem) => {
      if (cartItem.cartItemId === item.cartItemId) {
        cartItem = item;
      }
      return cartItem;
    });
  }

  get totalItemsCount() {
    const count = $derived(
      this.cartItems.reduce((total, item) => {
        return total + (item.qty || 0); // Add fallback to 0 if qty is undefined
      }, 0),
    );

    return count || 0; // Add fallback to 0 if count is NaN
  }
}

export const setCartState = () => {
  const cartState = new CartState();
  return setContext(getCartKey(), cartState);
};

export const getCartState = () => {
  return getContext<ReturnType<typeof setCartState>>(getCartKey());
};
