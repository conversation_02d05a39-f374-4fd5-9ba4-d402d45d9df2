import {
  createBrowserSession,
  createIpSession,
  getStorageKey,
  type Session,
  type SessionKeyPrefix,
} from "$lib/server/session.ts";
import type { RequestEvent } from "@sveltejs/kit";
import { redis } from "$lib/server/redis.server.ts";
import { logger } from "$lib/logger/logger.svelte";

class RateLimiter {
  keyPrefix: SessionKeyPrefix;
  limit: number;
  windowInSeconds: number;

  constructor({
    keyPrefix,
    limit,
    windowInSeconds,
  }: {
    keyPrefix: SessionKeyPrefix;
    limit: number;
    windowInSeconds: number;
  }) {
    this.keyPrefix = keyPrefix;
    this.limit = limit;
    this.windowInSeconds = windowInSeconds;
  }

  async getSessionId(event: RequestEvent): Promise<string> {
    if (this.keyPrefix === "ip") {
      const ip = event.request.headers.get("X-Real-Ip");
      if (!ip) {
        logger.error({ ip, event }, "IP not found");
        throw new Error("IP not found");
      }
      return ip;
    } else if (this.keyPrefix === "session") {
      let sessionId = event.cookies.get("session_id");

      if (!sessionId) {
        sessionId = (await createBrowserSession(event, this.windowInSeconds))
          .sessionId;
      }

      return sessionId;
    }
    throw new Error("Invalid key prefix");
  }

  async checkAndIncrement(event: RequestEvent): Promise<{ exceedsLimit: boolean }> {
    /**
     * If this request is made internally (server to server), never rate limit
     */
    if (event.isSubRequest) {
      return {exceedsLimit: false}
    }

    const id = await this.getSessionId(event);

    let key = getStorageKey(this.keyPrefix, id);

    const sessionString = await redis.get(key);

    if (!sessionString && this.keyPrefix === "ip") {
      const { ip } = await createIpSession(event, this.windowInSeconds);
      key = getStorageKey(this.keyPrefix, ip);
    }

    if (!sessionString && this.keyPrefix === "session") {
      const { sessionId } = await createBrowserSession(
        event,
        this.windowInSeconds,
      );
      key = getStorageKey(this.keyPrefix, sessionId);
    }

    const session: Session = sessionString
      ? JSON.parse(sessionString)
      : { requests: { totalRequests: 0 } };

    // TODO If session comes back as the wrong shape, should we generate a new session? Think api changes. Is it
    //  possible to abuse this as an attacker? Otherwise, good faith users could have trouble getting to the website
    //  without clearing their cookies or worse, somehow having to reset their ip.

    const requestRouteId = event.route.id;
    if (requestRouteId) {
      session.requests[requestRouteId] = session.requests[requestRouteId]
        ? session.requests[requestRouteId] + 1
        : 1;
      session.requests.totalRequests += 1;
    }

    const totalRequests = session.requests.totalRequests;

    if (totalRequests >= this.limit) {
      return { exceedsLimit: true };
    }

    await redis.set(key, JSON.stringify(session), "KEEPTTL");

    return { exceedsLimit: false };
  }
}

// TODO Might be useful to make the rate limiters pull their config dynamically
export const ipRateLimiter = new RateLimiter({
  keyPrefix: "ip",
  limit: 1000,
  windowInSeconds: 60 * 30,
});

export const browserRateLimiter = new RateLimiter({
  keyPrefix: "session",
  limit: 500,
  windowInSeconds: 60 * 30,
});
