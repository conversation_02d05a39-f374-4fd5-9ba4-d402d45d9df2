import {
  filterHoliday,
  mapDayToName,
  storeInfoHoursDisplay,
} from "$lib/helpers/store-info";
import type { ActiveHours, ActiveWindow, StoreInfo } from "$lib/types";
import { getContext, setContext } from "svelte";

const STORE_INFO_KEY = Symbol("storeInfo");

export class StoreInfoState {
  private _info = $state<StoreInfo | null>(null);

  constructor(initialData?: StoreInfo) {
    if (initialData) {
      this.setStoreInfo(initialData);
    }
  }

  setStoreInfo(data: StoreInfo) {
    this._info = data;
  }

  get info() {
    return (
      this._info ?? {
        name: "",
        address: "",
        city: "",
        state: "",
        phone: "",
        onlineActive: false,
        storeHours: {
          mon: [],
          tue: [],
          wed: [],
          thu: [],
          fri: [],
          sat: [],
          sun: [],
          xmas: [],
          xmasEve: [],
          nwYrs: [],
          nwYrsEve: [],
          thanks: [],
          ind: [],
          labor: [],
          memor: [],
          colum: [],
          vets: [],
          pres: [],
          mlk: [],
        },
      }
    );
  }

  get isOnlineActive() {
    return this.info?.onlineActive ?? false;
  }

  get name() {
    return this.info?.name ?? "";
  }

  get phone() {
    return this.info?.phone ?? "";
  }

  get formattedAddress() {
    if (!this.info) return "";
    return `${this.info.address}, ${this.info.city}, ${this.info.state}`;
  }

  get storeHours() {
    return this.info?.storeHours ?? {};
  }

  get formattedStoreHours() {
    return storeInfoHoursDisplay(this.info);
  }

  get allStoreHours() {
    return Object.entries(this.storeHours).map(([day, hours]) => {
      return {
        day: mapDayToName(day as keyof ActiveHours),
        hours,
      };
    });
  }

  get weekDayHours() {
    return Object.entries(this.storeHours)
      .map(([day, hours]) => {
        const dayName = filterHoliday(mapDayToName(day));
        if (!dayName) return null;
        return {
          day: dayName,
          hours,
        };
      })
      .filter((item): item is { day: string; hours: ActiveWindow[] } =>
        Boolean(item),
      );
  }
}

export const setStoreInfoState = (initialData?: StoreInfo) => {
  return setContext(STORE_INFO_KEY, new StoreInfoState(initialData));
};

export const getStoreInfoState = () => {
  return getContext<ReturnType<typeof setStoreInfoState>>(STORE_INFO_KEY);
};
