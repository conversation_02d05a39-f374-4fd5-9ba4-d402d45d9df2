import type { DetailedItem, SvelteFetch, Totals } from "$lib/types";
import type { IBridgeClient } from "$lib/client/bridge/bridge-client.ts";
import {
  getDetailedTestData,
  getTestData,
  restaurants,
} from "../../../test-data";
import * as restaurant1 from "../../../test-data/restaurants/restaurant-1/menu";
import * as restaurant2 from "../../../test-data/restaurants/restaurant-2/menu";
import { publicConfig } from "../../../publicConfig";

/**
 * Mock implementation of BridgeClient for testing purposes
 * This class mimics the real BridgeClient but returns mock data instead of making actual API calls
 */
export class MockBridgeClient implements IBridgeClient {
  fetch: SvelteFetch;
  deviceId: string;

  constructor(fetch: SvelteFetch, deviceId: string) {
    this.fetch = fetch;
    this.deviceId = deviceId;
  }

  async getMenu() {
    return getTestData(this.deviceId);
  }

  async getDetailedItem(itemId: string): Promise<DetailedItem> {
    const fetch = () => getDetailedTestData(itemId);

    if (publicConfig.CI) return fetch();

    const delay = Math.random() * 3000;
    return new Promise((resolve) => setTimeout(() => resolve(fetch()), delay));
  }

  async getHealth() {
    const restaurantIndex = restaurants.findIndex(
      (restaurant) => restaurant.device_id === this.deviceId,
    );

    switch (restaurantIndex) {
      case 0:
        return Promise.resolve({ ok: restaurant1.storeInfo.onlineActive });
      case 1:
        return Promise.resolve({ ok: restaurant2.storeInfo.onlineActive });
      default:
        return Promise.resolve({ ok: false });
    }
  }

  async getTotals({
    items,
    promisedTime,
  }: {
    items: DetailedItem[];
    promisedTime: number;
  }): Promise<Totals> {
    // TODO Make dynamic
    const cartTotal = 25;

    console.log("Promised time: ", promisedTime);

    return Promise.resolve({
      discountTotal: 0,
      gratuityTotal: 0,
      subTotal: cartTotal,
      takeOutSurchargeTotal: 50,
      taxTotal: cartTotal * 0.08, // 8% mock tax
      taxTotals: [cartTotal * 0.08],
      total: cartTotal * 1.08,
      items: items,
      blockedReason: [],
    });
  }
}
