import type {
  BasicDepartment,
  BasicItem,
  DetailedItem,
  StoreInfo,
  SvelteFetch,
  Totals,
} from "$lib/types";
import { getContext, setContext } from "svelte";
import { CheckoutError } from "$lib/services/errors/checkout/CheckoutError.ts";

export interface IBridgeClient {
  getMenu(): Promise<{
    departments: BasicDepartment[];
    items: BasicItem[];
    storeInfo: StoreInfo;
  }>;

  getDetailedItem(itemId: string): Promise<DetailedItem>;

  getHealth(): Promise<{ ok: boolean }>;

  getTotals(requestData: {
    items: DetailedItem[];
    promisedTime: number;
    subTotal: number;
  }): Promise<Totals>;
}

/**
 * Client for making authenticated requests to the bridge API, which exists as a middleman for communicating with
 * POS systems.
 */
export class BridgeClient implements IBridgeClient {
  fetch: SvelteFetch;
  deviceId: string;

  constructor(fetch: SvelteFetch, deviceId: string) {
    this.fetch = fetch;
    this.deviceId = deviceId;
  }

  async getMenu() {
    const response = await this.fetch(
      `/api/bridge/${this.deviceId}/item/online/menu`,
      {
        method: "GET",
      },
    );

    const data = (await response.json()) as {
      departments: BasicDepartment[];
      items: { [departmentId: string]: BasicItem[] };
      storeInfo: StoreInfo;
    };

    const itemsFlattened = Object.values(data.items ?? []).flatMap((items) => {
      /**
       * Handle any falsy response we may get back from the api by ignoring.
       */
      if (!items) {
        return [];
      }
      return items;
    });

    return {
      departments: data.departments,
      items: itemsFlattened,
      storeInfo: data.storeInfo,
    };
  }

  async getDetailedItem(itemId: string) {
    const response = await this.fetch(
      `/api/bridge/${this.deviceId}/item/online/${itemId}`,
      {
        method: "GET",
      },
    );

    const data = (await response.json()) as DetailedItem;

    return data;
  }

  async getTotals(requestData: {
    items: DetailedItem[];
    promisedTime: number;
    subTotal: number;
  }): Promise<Totals> {
    const response = await this.fetch(
      `/api/bridge/${this.deviceId}/sale/online/totals`,
      {
        method: "POST",
        body: JSON.stringify(requestData),
      },
    );

    const totals = await response.json();
    if (!response.ok) {
      throw new CheckoutError({
        name: totals.name || "GET_TOTALS",
        message: totals.message || "Failed to get totals",
        cause: totals.cause,
      });
    }

    return totals;
  }

  async getHealth() {
    try {
      const response = await this.fetch(
        `/api/bridge/${this.deviceId}/sale/online/health`,
        {
          method: "GET",
        },
      );

      const { ok } = await response.json();

      if (!ok) {
        throw new CheckoutError({
          name: "RESTAURANT_OFFLINE",
          message: "Restaurant is offline and cannot process orders",
          cause: response,
        });
      }

      return { ok };
    } catch (error) {
      throw new CheckoutError({
        name: "HEALTH_CHECK_FAILED",
        message: "Health check failed",
        cause: error,
      });
    }
  }
}

export const getBridgeClient = (): IBridgeClient => {
  return getContext<IBridgeClient>("bridgeClient");
};

export const setBridgeClient = (bridgeClient: IBridgeClient) => {
  return setContext("bridgeClient", bridgeClient);
};
