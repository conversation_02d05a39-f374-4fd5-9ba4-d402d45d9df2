import { processPayment } from "$lib/api/process-payment";
import type { OrderInfo, NmiResponse } from "$lib/types";

type QueryClient = {
  processPayment: (args: {
    nmiToken: string;
    orderInfo: OrderInfo;
    deviceId: string;
  }) => Promise<{ paymentSuccessful: boolean; paymentResult: NmiResponse; emailSent: boolean }>;
};

const createClient = (): QueryClient => {
  return {
    processPayment,
  };
};

// TODO Maybe use get and set context here
export const client = createClient();
