import mjml2html from "mjml";

export const getMjmlTemplate = async (templateName: string): Promise<string> => {
    try {
        const mjmlFile = await import(`$lib/email-templates/${templateName}.mjml?raw`);
        const mjmlContent = mjmlFile.default;
        const { html } = mjml2html(mjmlContent);
        return html;
    } catch (error) {
        console.error('Error loading MJML template:', error);
        throw new Error('Failed to load MJML template');
    }
}