import type { BasicItem, DetailedItem } from "$lib/types";

export const hasModifiers = (
  item: BasicItem | DetailedItem,
): item is DetailedItem => {
  return "modifiers" in item && item.modifiers.length > 0;
};
/**
 * Checks if the item is a detailed item
 * @param item - The item to check
 * @returns True if the item has 'isVisible' property, 
 * which is only present in detailed items
 */
export const isDetailedItem = (
  item: BasicItem | DetailedItem,
): item is DetailedItem => {
  return "isVisible" in item;
};

export const hasSomeSelectedChildren = (item: DetailedItem): boolean =>
  item.modifiers?.some((mod) => mod.selected) ?? false;
