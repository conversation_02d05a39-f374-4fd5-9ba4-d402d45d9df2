import { queryStringConstructor } from "./route-helpers";

export const routes = {
  main: (restaurant: string) => `/${restaurant}`,
  checkout: (restaurant: string) => `/${restaurant}/checkout`,
  thankYou: ({
    transactionId,
    restaurant,
    emailSent,
  }: {
    transactionId: string;
    restaurant: string;
    emailSent: boolean;
  }) => {
    const queryString = queryStringConstructor({
      params: [{ restaurant }, { emailed: emailSent }],
    });

    return `/thank-you/${transactionId}${queryString ? `?${queryString}` : ""}`;
  },
};
