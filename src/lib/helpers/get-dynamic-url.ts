import { publicConfig } from "../../publicConfig.ts";

const isBrowser = typeof window !== "undefined";

export const getApiBaseUrl = (deviceId: string | undefined) => {
  if (publicConfig.env !== "dev") {
    if (!deviceId) {
      throw new Error("Device ID is required");
    }
    return `${getBalenaUrl(deviceId)}/bridge/api/v1`;
  }

  if (isBrowser) {
    // This assumes that the bridge server is running on the same machine as the online ordering server. This allows us to dynamically resolve the host no matter what the address is.
    return `${window.location.protocol}//${window.location.hostname}:8880`;
  }
  return "http://localhost:8880";
};

export const getBalenaUrl = (deviceId: string) =>
  `https://${deviceId}.balena-devices.com`;
