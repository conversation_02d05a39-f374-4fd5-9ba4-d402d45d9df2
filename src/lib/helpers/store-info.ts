import type { ActiveHours, ActiveWindow, StoreInfo } from "$lib/types";
import { formatMillisToTime, getLocalTimeInMs } from "./time.ts";

const FORTY_FIVE_MINUTES_IN_MS = 45 * 60 * 1000;

const dayMap: Record<string, string> = {
  sun: "Sunday",
  mon: "Monday",
  tue: "Tuesday",
  wed: "Wednesday",
  thu: "Thursday",
  fri: "Friday",
  sat: "Saturday",
  xmas: "Christmas",
  xmasEve: "Christmas Eve",
  nwYrs: "New Year's Day",
  nwYrsEve: "New Year's Eve",
  thanks: "Thanksgiving",
  ind: "Independence Day",
  labor: "Labor Day",
  memor: "Memorial Day",
  colum: "Columbus Day",
  vets: "Veterans Day",
  pres: "Presidents' Day",
  mlk: "Martin Luther King Jr. Day",
};

const weekdays = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
];

export const getCurrentDayKey = () => {
  const daysOfWeek: (keyof ActiveHours)[] = [
    "sun",
    "mon",
    "tue",
    "wed",
    "thu",
    "fri",
    "sat",
  ];
  const today = new Date().getDay();
  return daysOfWeek[today];
};

/**
 *
 * @returns {object} containing:
 * - closingSoon: boolean indicating if closing within 45 minutes
 * - timeUntilClosingInMs:
 *   - 0 = Currently closed
 *   - > 0 = Currently open (milliseconds until closing)
 * - timeUntilOpeningInMs: Only present when temporarily closed, indicates ms until next opening
 * - currentWindow: The active operating window, or null if closed
 */
export const isClosingSoon = ({
  hoursForToday,
}: {
  hoursForToday: ActiveWindow[];
}): {
  closingSoon: boolean;
  timeUntilClosingInMs?: number;
  /**
   * We only get this property if we are "in between" ActiveHours arrays.
   * This is the time until the next opening window.
   */
  timeUntilOpeningInMs?: number;
  currentWindow: ActiveWindow | null;
} => {
  if (!hoursForToday) {
    return {
      closingSoon: false,
      timeUntilClosingInMs: 0,
      timeUntilOpeningInMs: undefined,
      currentWindow: null,
    };
  }

  hoursForToday.sort((a, b) => a.open - b.open);

  /**
   * TODO: This is very bad.
   * ! We need to get the bridge to send us time in UTC
   * ! What if the restaurant is in a different timezone?
   */
  const currentTimeInMs = getLocalTimeInMs();

  const currentWindow = hoursForToday.find(
    (window) =>
      currentTimeInMs >= window.open && currentTimeInMs <= window.close,
  );

  if (hoursForToday.length > 0 && !currentWindow) {
    const futureOpeningWindow = hoursForToday.find(
      (window) => currentTimeInMs <= window.open,
    );

    if (futureOpeningWindow) {
      const timeUntilOpeningInMs = futureOpeningWindow.open - currentTimeInMs;
      return {
        closingSoon: false,
        timeUntilClosingInMs: 0,
        timeUntilOpeningInMs,
        currentWindow: null,
      };
    }
  }

  if (!currentWindow) {
    return {
      closingSoon: false,
      timeUntilClosingInMs: undefined,
      timeUntilOpeningInMs: undefined,
      currentWindow: null,
    };
  }

  const closingTime = currentWindow.close;
  const timeUntilClosingInMs = closingTime - currentTimeInMs;

  const isClosingSoon =
    timeUntilClosingInMs > 0 &&
    timeUntilClosingInMs <= FORTY_FIVE_MINUTES_IN_MS;

  return {
    closingSoon: isClosingSoon,
    timeUntilClosingInMs,
    currentWindow,
  };
};

export const storeInfoHoursDisplay = (storeInfo: StoreInfo) => {
  const { storeHours } = storeInfo;
  const currentDayKey = getCurrentDayKey();
  const hoursForToday = currentDayKey ? storeHours[currentDayKey] : undefined;

  if (!hoursForToday || hoursForToday.length === 0) {
    return ["Closed today"];
  }

  return hoursForToday.map((hour) => {
    return `${formatMillisToTime(hour.open)} - ${formatMillisToTime(hour.close)}`;
  });
};

export const mapDayToName = (day: string) => {
  return dayMap[day] || day;
};

export const filterHoliday = (day: string) => {
  if (weekdays.includes(day)) {
    return day;
  }
  return null;
};
