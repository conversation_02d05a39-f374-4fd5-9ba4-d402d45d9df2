import { logger } from "$lib/logger/logger.svelte";
import type { SelectedTip } from "../types/selectedTip";

export const calcTip = (
  selectedTipOption: SelectedTip,
  total: number,
  tipInput: string | number,
) => {
  const tip = Number(tipInput);

  let finalTip: number;

  if (tip < 0 || selectedTipOption === "none") {
    finalTip = 0;
  } else if (selectedTipOption === "custom") {
    finalTip = tip * 100;
  } else {
    const percentageNumber = Number(selectedTipOption.replace("%", ""));
    const percentage = percentageNumber / 100;
    finalTip = total * percentage;
  }

  if (!Number.isFinite(finalTip)) {
    const message = `Failed to calculate tip`;
    logger.error({ finalTip, tip, total, selectedTipOption }, message);
    return 0;
  }

  return finalTip;
};
