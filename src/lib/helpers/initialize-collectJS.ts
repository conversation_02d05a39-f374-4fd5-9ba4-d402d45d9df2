import { centToDollar } from "$lib/helpers/number.ts";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
declare let CollectJS: any;

interface CallbackResponse {
  card: {
    bin: string;
    exp: string;
    hash: string;
    number: string;
    type: string;
  };
  check: {
    aba: string | null;
    account: string | null;
    hash: string | null;
    institution: string | null;
    name: string | null;
    transit: string | null;
  };
  initiatedBy: string | undefined;
  token: string;
  tokenType: string;
}

interface CollectJSResult {
  token: string;
  cardLastFour: string;
}

export const fieldValidationStates = {
  ccnumber: false,
  ccexp: false,
  cvv: false,
};

export const initializeCollectJS = ({
  tokenizationKey,
  cartTotal,
}: {
  tokenizationKey: string;
  cartTotal: number;
}): Promise<CollectJSResult> =>
  new Promise((resolve, reject) => {
    const script = document.createElement("script");
    script.src = "https://secure.networkmerchants.com/token/Collect.js";
    script.dataset.tokenizationKey = tokenizationKey;
    script.dataset.fieldCcnumberEnableCardBrandPreviews = "true";
    script.async = true;

    script.onload = () => {
      console.log("loading Collect JS...");
      CollectJS.configure({
        price: centToDollar(cartTotal),
        currency: "USD",
        country: "US",
        variant: "inline",
        nscript: true,
        fields: {
          ccnumber: {
            selector: "#ccNumber",
            title: "Card Number",
            placeholder: "0000 0000 0000 0000",
          },
          ccexp: {
            selector: "#ccExp",
            title: "Card Expiration",
            placeholder: "MM / YY",
          },
          cvv: {
            display: "show",
            selector: "#ccCVV",
            title: "CVV",
            placeholder: "***",
          },
        },
        invalidCss: {
          // collect JS does not support css variables
          color: "#dc2626",
          "border-color": "#dc2626",
          "border-width": "2px",
          "background-color": "#fee2e2",
        },
        validCss: {
          color: "#16a34a",
          "border-color": "#16a34a",
          "border-width": "2px",
          "background-color": "#dcfce7",
        },
        focusCss: {
          "border-width": "4px",
        },
        callback: (response: CallbackResponse) => {
          if (response.token && response.card.number) {
            resolve({
              token: response.token,
              cardLastFour: response.card.number.slice(-4),
            });
          } else {
            reject(new Error("No token or card number generated in callback"));
          }
        },
        validationCallback: (
          field: string,
          status: boolean,
          message: string,
        ) => {
          console.log(
            `Field ${field} validation: ${status ? "passed" : "failed"} - ${message}`,
          );
          fieldValidationStates[field as keyof typeof fieldValidationStates] =
            status;
        },
      });
      console.log("Collect.js has been loaded");
    };

    script.onerror = () => {
      reject(new Error("Failed to load Collect.js script"));
    };

    document.head.appendChild(script);
  });
