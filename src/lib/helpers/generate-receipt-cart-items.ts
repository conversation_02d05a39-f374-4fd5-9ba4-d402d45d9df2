import { centToDollar } from "$lib/helpers/number.ts";
import type { CartState } from "$lib/services/Cart.svelte.ts";

export const generateCartItemsHtmlRows = (
  items: CartState["checkoutItems"],
): string => {
  return items
    .map(
      (item) => `
    <tr>
      <td style="padding: 15px 0">${item.desc}</td>
      <td style="padding: 15px 0">${item.qty}</td>
      <td style="padding: 15px 0">$${centToDollar(item.price)}</td>
    </tr>
  `,
    )
    .join("");
};
