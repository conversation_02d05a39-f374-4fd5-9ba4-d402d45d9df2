import { CheckoutError } from "$lib/services/errors/checkout/CheckoutError";

export const validateCheckoutFields = (
  customerFirstName: string,
  customerLastName: string,
  customerEmail: string,
  customerPhone: string,
): void => {
  if (!customerFirstName.trim()) {
    throw new CheckoutError({
      name: "FIRST_NAME_REQUIRED",
      message: "Please enter your first name.",
    });
  }

  if (!customerLastName.trim()) {
    throw new CheckoutError({
      name: "LAST_NAME_REQUIRED",
      message: "Please enter your last name.",
    });
  }

  if (!customerEmail.trim()) {
    throw new CheckoutError({
      name: "EMAIL_REQUIRED",
      message: "Please enter your email.",
    });
  }

  if (!customerPhone.trim()) {
    throw new CheckoutError({
      name: "PHONE_REQUIRED",
      message: "Please enter your phone number.",
    });
  }
};
