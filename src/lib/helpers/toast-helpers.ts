export const getContainerStyle = (position: string) => {
  const baseStyle = `position: fixed; display: flex;`;
  switch (position) {
    case "center-t":
      return `${baseStyle} top: 0; left: 50%; transform: translateX(-50%); flex-direction: column; margin-top: 40px;`;
    case "center-b":
      return `${baseStyle} bottom: 0; left: 50%; transform: translateX(-50%); flex-direction: column-reverse; margin-bottom: 40px;`;
    case "top-r":
      return `${baseStyle} top: 0; right: 10px; flex-direction: column; margin-top: 40px;`;
    case "top-l":
      return `${baseStyle} top: 0; left: 10px; flex-direction: column; margin-top: 40px;`;
    case "bottom-r":
      return `${baseStyle} bottom: 0; right: 10px; flex-direction: column-reverse; margin-bottom: 40px;`;
    case "bottom-l":
      return `${baseStyle} bottom: 0; left: 10px; flex-direction: column-reverse; margin-bottom: 40px;`;
    default:
      return `${baseStyle} top: 0; left: 50%; transform: translateX(-50%); flex-direction: column; margin-top: 40px;`;
  }
};

type ColorOption = "success" | "error" | "warning" | "info" | "default";
type ColorsType = Record<ColorOption, { background: string; border: string }>

export const getToastStyle = (type: ColorOption): string => {
  const colors: ColorsType = {
    success: { background: "#f0fdf4", border: "#02ba0b" },
    error: { background: "#fef2f2", border: "#ba0202" },
    warning: { background: "#fef9c3", border: "#ba9202" },
    info: { background: "#e0f2fe", border: "#1b61d1" },
    default: { background: "#ffffff", border: "#1b61d1" },
  };

  const { background, border } = colors[type] || colors.default;

  return `
    background-image: linear-gradient(to right, ${background}, #ffffff, #ffffff);
    border-color: ${border};
  `;
};

export const flyFrom = (position: string) => {
  return position.includes("b") ? 30 : -30;
};
