import { logger } from "$lib/logger/logger.svelte";
import type { DetailedItem } from "$lib/types";

// TODO Write a unit test for this
export const calculateTotalItemPrice = (
  item: DetailedItem[] | DetailedItem,
): number => {
  if (Array.isArray(item)) {
    let total = 0;
    item.forEach((item) => {
      if (item.selected) {
        total += item.price * item.qty;
      }
      if (item.modifiers?.length > 0) {
        const nestedTotal = calculateTotalItemPrice(item.modifiers);
        total += nestedTotal;
      }
    });

    if (!Number.isFinite(total)) {
      const message = "Failed to calculate total item price";
      logger.error({ total, item }, message);
      throw new Error(message);
    }
    return total;
  } else {
    const basePrice = item.price * item.qty;
    let nestedTotal = 0;
    if (item.modifiers?.length > 0) {
      nestedTotal = calculateTotalItemPrice(item.modifiers);
    }
    const total = basePrice + nestedTotal;

    if (!Number.isFinite(total)) {
      const message = "Failed to calculate total item price";
      logger.error({ total, item }, message);
      throw new Error(message);
    }
    return total;
  }
};
