import { logger } from "$lib/logger/logger.svelte";

const MS_IN_SECOND = 1000;
const MS_IN_MINUTE = 60 * MS_IN_SECOND;
const MS_IN_HOUR = 60 * MS_IN_MINUTE;

export const formatMillisToTime = (ms: number): string => {
  const totalMinutes = ms / MS_IN_MINUTE;
  const hours = Math.floor(totalMinutes / 60);
  const minutes = Math.floor(totalMinutes % 60);

  const period = hours >= 12 ? "PM" : "AM";
  const hour12 = hours % 12 === 0 ? 12 : hours % 12;
  const minuteStr = minutes.toString().padStart(2, "0");

  if (!Number.isFinite(hour12) || !Number.isFinite(Number(minuteStr))) {
    logger.error(
      { ms, hour12, minuteStr },
      "Failed to format milliseconds to time",
    );
    return "";
  }

  return `${hour12}:${minuteStr} ${period}`;
};

export const formatMillisToMinutes = (ms: number): string => {
  const totalMinutes = ms / MS_IN_MINUTE;
  if (totalMinutes < 1) {
    return "less than 1";
  }
  return Math.floor(totalMinutes).toString();
};

export const formatMillisToHoursAndMinutes = (
  ms: number,
): {
  hours: number;
  minutes: number;
} => {
  const hours = Math.floor(ms / MS_IN_HOUR);
  const remainingMinutes = Math.floor((ms % MS_IN_HOUR) / MS_IN_MINUTE);
  return {
    hours,
    minutes: remainingMinutes,
  };
};

/**
 * Gets time in milliseconds where 0 = 12am. If running in browser, should automatically utilize local time.
 */
export const getLocalTimeInMs = () => {
  const now = new Date();

  return (
    now.getHours() * MS_IN_HOUR +
    now.getMinutes() * MS_IN_MINUTE +
    now.getSeconds() * MS_IN_SECOND +
    now.getMilliseconds()
  );
};
