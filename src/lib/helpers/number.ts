export const priceFormatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
});

export const centToDollar = (cents: number) => {
  return (cents / 100).toLocaleString("en-US", {
    style: "currency",
    currency: "USD",
  });
};

export const twoDecimalValidator = (value: string) => {
  if (value === "" || value === null || !value) return true;
  return /^\d+(\.\d{0,2})?$/.test(value);
};

/**
 * https://stackoverflow.com/questions/46321683/javascript-restrict-input-once-2-decimal-places-have-been-reached
 */
export const validateTwoDecimalInput = (e: Event) => {
  const target = e.target as HTMLInputElement;
  const t = target.value;

  // Save the current caret position
  const caretPosition = target.selectionStart;

  if (t.indexOf(".") >= 0) {
    target.value = t.slice(0, t.indexOf(".") + 3);
  }

  // Restore the caret position after modifying the value
  if (caretPosition !== null) {
    target.setSelectionRange(caretPosition, caretPosition);
  }
};

/**
 *
 * https://www.youtube.com/watch?v=3Cl03G2cyAA
 */
export const formatPhoneNumber = (value: string) => {
  if (!value) return value;

  const phoneNumber = value.replace(/[^\d]/g, "");
  const phoneNumberLength = phoneNumber.length;

  if (phoneNumberLength < 4) return phoneNumber;

  if (phoneNumberLength < 7)
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;

  return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
};

/**
 * Validates the custom tip input to ensure it is a valid number with up to 3 digits before the decimal point.
 */
export const sanitizeCustomTipInput = (value: string) => {
  if (!value) return value;

  const tipAmount = value.replace(/[^0-9.]/g, "");

  const parts = tipAmount.split(".");
  const beforeDecimal = parts[0] || "";
  const afterDecimal = parts[1];

  // Limit to 3 digits before decimal
  const limitedBeforeDecimal = beforeDecimal.slice(0, 3);
  // Limit to 2 digits after decimal
  const twoDecimalPlaces = afterDecimal?.slice(0, 2);

  if (afterDecimal !== undefined) {
    return `${limitedBeforeDecimal}.${twoDecimalPlaces}`;
  }

  return limitedBeforeDecimal;
};
