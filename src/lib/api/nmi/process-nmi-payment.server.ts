import { logger } from "$lib/logger/logger.svelte.ts";
import { getNMIKey } from "$lib/server/getNMIKey.server.ts";
import {
  OrderState,
  OrderStateTracker,
} from "$lib/services/OrderStateTracker.server.ts";
import type { NmiResponse, OrderInfo, SvelteFetch } from "$lib/types";
import { error } from "@sveltejs/kit";
import { z } from "zod/v4";
import { privateConfig } from "../../../privateConfig.server.ts";
import { publicConfig } from "../../../publicConfig.ts";

export type ProcessNmiPayment = (args: {
  fetch: SvelteFetch;
  nmiToken: string;
  orderInfo: OrderInfo;
  deviceId: string;
}) => Promise<NmiResponse>;

/**
 * Copied from https://docs.nmi.com/reference/process
 */
export const nmiResponseCodes: Record<
  z.infer<typeof nmiResponseCodeSchema>,
  string
> = {
  100: "Transaction was approved.",
  200: "Transaction was declined by processor.",
  201: "Do not honor.",
  202: "Insufficient funds.",
  203: "Over limit.",
  204: "Transaction not allowed.",
  220: "Incorrect payment information.",
  221: "No such card issuer.",
  222: "No card number on file with issuer.",
  223: "Expired card.",
  224: "Invalid expiration date.",
  225: "Invalid card security code.",
  226: "Invalid PIN.",
  240: "Call issuer for further information.",
  250: "Pick up card.",
  251: "Lost card.",
  252: "Stolen card.",
  253: "Fraudulent card.",
  260: "Declined with further instructions available. (See response text)",
  261: "Declined-Stop all recurring payments.",
  262: "Declined-Stop this recurring program.",
  263: "Declined-Update cardholder data available.",
  264: "Declined-Retry in a few days.",
  300: "Transaction was rejected by gateway.",
  400: "Transaction error returned by processor.",
  410: "Invalid merchant configuration.",
  411: "Merchant account is inactive.",
  420: "Communication error.",
  421: "Communication error with issuer.",
  430: "Duplicate transaction at processor.",
  440: "Processor format error.",
  441: "Invalid transaction information.",
  460: "Processor feature not available.",
  461: "Unsupported card type.",
} as const;

const nmiResponseCodeSchema = z.literal([
  "100",
  "200",
  "201",
  "202",
  "203",
  "204",
  "220",
  "221",
  "222",
  "223",
  "224",
  "225",
  "226",
  "240",
  "250",
  "251",
  "252",
  "253",
  "260",
  "261",
  "262",
  "263",
  "264",
  "300",
  "400",
  "410",
  "411",
  "420",
  "421",
  "430",
  "440",
  "441",
  "460",
  "461",
]);

/**
 * https://docs.nmi.com/reference/process
 * Schema is typed based on what we need. For example, `responsetext` should always be present, but it's not essential for us, so we make it optional just in case.
 */
export const nmiResponseSchema = z.object({
  /**
   * Status code for the operation. (1) - success, (2) - failed, (3) system error
   */
  response: z.literal(["1", "2", "3"]),
  /**
   * (Conditional - Payment) Payment gateway transaction id.
   * This is actually optional on Nmi's end, but we require it for our own purposes.
   */
  transactionid: z.string(),
  /**
   * Identifier for an order related to this invoice.
   */
  orderid: z.string(),
  /**
   * Authorization code for the operation.
   */
  authcode: z.string(),
  /**
   * Another status or result code for the operation. [See Response Code Table](https://docs.nmi.com/reference/process#section/Appendixes/Payment-API-Response-Codes)
   */
  response_code: nmiResponseCodeSchema,
  /**
   * Textual description of the operation's result.
   */
  responsetext: z.string().optional(),
  /**
   * Address Verification Service response code.
   */
  avsresponse: z.string().optional(),
  /**
   * Card Verification Value response.
   */
  cvvresponse: z.string().optional(),

  /**
   * Type of operation that this response is for.
   */
  type: z
    .literal([
      "sale",
      "auth",
      "validate",
      "credit",
      "offline",
      "capture",
      "void",
      "refund",
      "complete_partial_payment",
      "add_invoice",
      "update_invoice",
      "send_invoice",
      "close_invoice",
      "add_plan",
      "edit_plan",
      "add_subscription",
    ])
    .optional(),
});

const parseNmiResponse = async (response: Response) => {
  const result = Object.fromEntries(new URLSearchParams(await response.text()));

  // Parse URL-encoded format
  const parsedResult = nmiResponseSchema.safeParse(result);

  if (!parsedResult.success) {
    logger.error(
      { nmiResponse: result, error: parsedResult.error },
      "Received unexpected NMI response shape",
    );
  }

  return parsedResult.data;
};

const sendPaymentRequest = async ({
  fetch,
  deviceId,
  orderInfo,
  nmiToken,
  amount,
  logInfo,
}: {
  fetch: SvelteFetch;
  deviceId: string;
  orderInfo: OrderInfo;
  nmiToken: string;
  amount: string;
  logInfo: Record<string, unknown>;
}): Promise<Response> => {
  const { customer } = orderInfo;
  const securityKey = ["dev", "staging"].includes(publicConfig.env ?? "")
    ? privateConfig.nmiTestKey
    : await getNMIKey(fetch, deviceId);

  const formData = new URLSearchParams({
    security_key: securityKey,
    type: "sale",
    amount,
    payment_token: nmiToken,
    first_name: customer.firstName,
    last_name: customer.lastName,
    email: customer.email ?? "",
    currency: "USD",
  });

  logger.info(logInfo, "Attempting payment submission");

  // https://docs.nmi.com/reference/process
  const response = await fetch(privateConfig.nmiUrl, {
    method: "POST",
    headers: {
      accept: "application/x-www-form-urlencoded",
      "content-type": "application/x-www-form-urlencoded",
    },
    body: formData,
  });

  return response;
};

export const processNmiPayment: ProcessNmiPayment = async ({
  fetch,
  nmiToken,
  orderInfo,
  deviceId,
}) => {
  const orderStateTracker = await OrderStateTracker.instance(orderInfo);
  const amount = (orderInfo.cart.total / 100).toFixed(2);
  const { customer } = orderInfo;

  // Safe logging (exclude sensitive info)
  const logInfo = {
    idempotencyKey: orderStateTracker.idempotencyKey,
    amount,
    customerPhone: customer.phone,
    deviceId,
  };

  // Record transaction start
  await orderStateTracker.updateState(OrderState.PENDING);

  const response = await sendPaymentRequest({
    fetch,
    deviceId,
    orderInfo,
    nmiToken,
    amount,
    logInfo,
  });

  await orderStateTracker.updateState(OrderState.PAYMENT_SUBMITTED);

  const responseBody = await parseNmiResponse(response);

  if (!responseBody) {
    return error(400, "Error processing payment");
  }

  if (responseBody.response !== "1") {
    await orderStateTracker.updateState(OrderState.PAYMENT_FAILED);

    const responseCode = nmiResponseCodeSchema.safeParse(
      responseBody.response_code,
    ).data;

    const status = nmiResponseCodeSchema.safeParse(response.status).data;
    // TODO Do these status messages come back from NMI? Maybe we don't need to actually map them.
    const nmiStatusMessage = status
      ? nmiResponseCodes[status]
      : "Something went wrong";

    logger.error(
      {
        ...logInfo,
        status,
        statusMessage: nmiStatusMessage,
        url: privateConfig.nmiUrl,
        responseCode: responseBody.response_code,
        responseCodeMessage: responseCode
          ? nmiResponseCodes[responseCode]
          : "Unknown",
      },
      "NMI payment request failed",
    );

    error(response.status, `Error processing payment: ${nmiStatusMessage}`);
  }

  // If we make it here, the payment response === `1`, which means it was successful
  await orderStateTracker.updateState(OrderState.PAYMENT_SUCCESS);

  logger.info(
    { ...logInfo, transactionId: responseBody.transactionid },
    "Payment successful",
  );

  return responseBody;
};
