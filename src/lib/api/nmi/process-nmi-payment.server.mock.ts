import type { ProcessNmiPayment } from "./process-nmi-payment.server";

export const processNmiPaymentMock: ProcessNmiPayment = (_args) => {

  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        response: "1", // 1 indicates success
        responsetext: "SUCCESS",
        authcode: "123456",
        transactionid: `MOCK-${Date.now()}`,
        avsresponse: "Y",
        cvvresponse: "M",
        orderid: "",
        type: "sale",
        response_code: "100",
      });
    }, 1000);
  });
};
