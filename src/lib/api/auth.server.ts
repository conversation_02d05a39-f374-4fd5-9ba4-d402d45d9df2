import jwksClient from "jwks-rsa";
import jwt, { type GetPublicKeyOrSecret, type Secret } from "jsonwebtoken";
import { privateConfig } from "../../privateConfig.server.ts";
import { logger } from "$lib/logger/logger.svelte.ts";

const TWO_DAYS_IN_MS = 86400 * 1000 * 2;
const TEN_MINUTES_IN_MS = 10 * 60 * 1000;

type TokenCache = {
  token: string;
  expiresAt: number;
};

type JwtVerifyArgs = Parameters<typeof jwt.verify>;

let tokenCache: TokenCache = {
  token: "",
  expiresAt: 0,
};

export async function getOrCreateBridgeApiToken() {
  const now = Date.now();

  try {
    if (tokenCache.token && tokenCache.expiresAt > now) {
      await verifyBridgeApiToken(tokenCache.token);
      return tokenCache.token;
    }
  } catch (err) {
    logger.warn({ tokenCache, err }, "Failed to validate token");
  }

  const token = await generateBridgeApiToken();

  tokenCache = {
    token,
    expiresAt: now + TWO_DAYS_IN_MS,
  };

  return token;
}

export async function generateBridgeApiToken() {
  logger.info("Generating a new bridge api token");
  const { authUrl, audienceUrl, clientId, clientSecret } = privateConfig;

  const fetchArguments = {
    authUrl,
    method: "POST",
    headers: {
      "content-type": "application/json",
    },
    body: JSON.stringify({
      client_id: clientId,
      client_secret: clientSecret,
      audience: audienceUrl,
      grant_type: "client_credentials",
    }),
  };

  logger.info(
    fetchArguments,
    "fetch arguments inside generateBridgeApiToken()",
  );

  if (!authUrl) {
    logger.error("No authUrl found in privateConfig");
    throw new Error("No authUrl found in privateConfig");
  }

  // This is the call that contributes to quota utilization
  const resp = await fetch(authUrl, {
    method: "POST",
    headers: {
      "content-type": "application/json",
    },
    body: JSON.stringify({
      client_id: clientId,
      client_secret: clientSecret,
      audience: audienceUrl,
      grant_type: "client_credentials",
    }),
  });

  if (!resp.ok) {
    logger.error(
      { resp },
      "Failed to generate bridge api token. Unauthenticated.",
    );
    throw new Error("Unauthenticated");
  }

  logger.info(resp, "fetch response inside generateBridgeApiToken()");
  const responseBody = await resp.json();

  logger.info(
    { bridgeApiAccessToken: responseBody.access_token },
    "Successfully parsed response json for bridge api token",
  );
  return responseBody.access_token;
}

export function jwtVerifyAsync(
  token: JwtVerifyArgs[0],
  secretOrPublicKey: JwtVerifyArgs[1],
): Promise<string | jwt.JwtPayload | undefined> {
  return new Promise((resolve, reject) => {
    jwt.verify(token, secretOrPublicKey, (err, decoded) => {
      if (err) {
        reject(err);
      }
      logger.info({ token, decoded }, "Successfully verified bridge api token");
      resolve(decoded);
    });
  });
}

export const verifyBridgeApiToken = async (token: string) => {
  logger.info({ token }, "Verifying bridge api token");
  const jwksUri = privateConfig.jwksUri;

  if (!jwksUri) {
    logger.error("No jwksUri found in privateConfig");
    throw new Error("No jwksUri found in privateConfig");
  }

  const client = jwksClient({
    jwksUri,
    requestHeaders: {},
    timeout: 5000, // fail fast if the request takes too long
    cache: true,
    cacheMaxAge: TEN_MINUTES_IN_MS,
    rateLimit: true,
    jwksRequestsPerMinute: 10,
  });

  const getKey: GetPublicKeyOrSecret = (header, getKeyCallback) => {
    client.getSigningKey(header.kid, (_, key) => {
      // @ts-expect-error These keys do exist on `key`. Not sure if we're missing some type narrowing.
      const signingKey = (key?.publicKey || key?.rsaPublicKey) as
        | Secret
        | undefined;
      getKeyCallback(null, signingKey);
    });
  };

  return jwtVerifyAsync(token, getKey);
};
