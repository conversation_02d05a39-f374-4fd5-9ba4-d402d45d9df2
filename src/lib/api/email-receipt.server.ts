import type { ReceiptEmailInfo } from "$lib/types";
import { generateCartItemsHtmlRows } from "$lib/helpers/generate-receipt-cart-items.ts";
import { sendEmail } from "$lib/api/emailer.server.ts";
import { centToDollar } from "$lib/helpers/number.ts";
import { logger } from "$lib/logger/logger.svelte";

/**
 * @param {ReceiptEmailInfo}
 * @returns success: boolean, error?: {name: string, message: string}
 * ! Do not throw an error here !
 * ! If it fails, we don't want to fail the entire checkout process.
 */
export const emailReceipt = async (
  receiptEmailInfo: ReceiptEmailInfo,
): Promise<{
  success: boolean;
  error?: { name: string; message: string };
}> => {
  const {
    cart,
    promisedTime,
    customer,
    paymentMethod,
    storeInfo,
    transactionid,
    card,
  } = receiptEmailInfo;

  if (!cart.items || cart.items.length === 0 || cart.totalItemsCount === 0) {
    return {
      success: false,
      error: {
        name: "EMAIL_SENDING_ERROR",
        message: "Cart is empty or invalid",
      },
    };
  }

  if (!customer.email || !customer.firstName || !customer.lastName) {
    return {
      success: false,
      error: {
        name: "EMAIL_SENDING_ERROR",
        message: "Customer email and name are required",
      },
    };
  }

  if (paymentMethod !== "credit-card") {
    return {
      success: false,
      error: {
        name: "EMAIL_SENDING_ERROR",
        message: "Only credit card payments are currently supported",
      },
    };
  }

  const cartItemsHtml = generateCartItemsHtmlRows(cart.items);

  if (!storeInfo.name) {
    return {
      success: false,
      error: {
        name: "EMAIL_SENDING_ERROR",
        message: "Failed to find merchant details for email",
      },
    };
  }

  try {
    await sendEmail({
      to: customer.email,
      subject: "Order Confirmation",
      templateName: "order-receipt",
      variables: {
        dynamicContent: cartItemsHtml,
        total: centToDollar(cart.subTotal),
        cartTip: `$${centToDollar(cart.tip)}`,
        salesTax: `$${cart.tax}`,
        grandTotal: centToDollar(cart.total),
        merchantAddress: storeInfo.address,
        merchantName: storeInfo.name,
        merchantPhone: storeInfo.phone,
        name: customer.firstName,
        invoiceNumber: transactionid,
        cardInfo: `**** **** **** ${card.pan}`,
        promisedTime: promisedTime.emailFormat,
      },
    });

    return { success: true };
  } catch (error) {
    logger.error({ error }, "Error sending email receipt");
    return {
      success: false,
      error: {
        name: "EMAIL_SENDING_ERROR",
        message: "Failed to send email receipt",
      },
    };
  }
};
