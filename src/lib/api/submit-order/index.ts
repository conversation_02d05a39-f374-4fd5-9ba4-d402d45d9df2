import {
  orderInfoToSubmitOrderPayload as orderInfoToSubmitOrderPayloadImpl,
  submitOrder as submitOrderImpl,
} from "./submit-order.server";
import { submitOrderMock } from "./submit-order.server.mock.ts";
import { publicConfig } from "../../../publicConfig.ts";

export const { submitOrder, orderInfoToSubmitOrderPayload } =
  publicConfig.env === "dev"
    ? {
        submitOrder: submitOrderMock,
        orderInfoToSubmitOrderPayload: orderInfoToSubmitOrderPayloadImpl,
      }
    : {
        submitOrder: submitOrderImpl,
        orderInfoToSubmitOrderPayload: orderInfoToSubmitOrderPayloadImpl,
      };
