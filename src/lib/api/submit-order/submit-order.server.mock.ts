import type { SubmitOrder } from "$lib/api/submit-order/submit-order.server.ts";
import type { CheckoutResponse } from "$lib/types";

// Re-export the utility function from the real implementation
export { orderInfoToSubmitOrderPayload } from "./submit-order.server.ts";

// Mock implementation of submitOrder
export const submitOrderMock: SubmitOrder = async (
  _args,
): Promise<CheckoutResponse> => {
  // Log the payload for debugging purposes

  // Simulate a network delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  // Mock successful response
  const mockResponse: CheckoutResponse = {
    saleID: "12345",
    tenders: [{ media: "card", amount: 100, voided: false, refunded: false }],
    message: "Order submitted successfully (mock)",
    total: 110,
    tipAmount: 10,
  };

  return mockResponse;
};
