import { getApiBaseUrl } from "$lib/helpers/get-dynamic-url.ts";
import { logger } from "$lib/logger/logger.svelte.ts";
import { CheckoutError } from "$lib/services/errors/checkout/CheckoutError.ts";
import {
  OrderState,
  OrderStateTracker,
} from "$lib/services/OrderStateTracker.server.ts";
import type {
  CheckoutResponse,
  NmiResponse,
  OrderInfo,
  SubmitOrderPayload,
  SvelteFetch,
} from "$lib/types";

export type SubmitOrder = (args: {
  fetch: SvelteFetch;
  orderInfo: OrderInfo;
  nmiDetails: NmiResponse;
  deviceId: string;
  bridgeApiToken: string;
}) => Promise<CheckoutResponse>;

export const orderInfoToSubmitOrderPayload = (
  orderInfo: OrderInfo,
  paymentDetails: NmiResponse,
): SubmitOrderPayload => {
  return {
    amount: Math.round(orderInfo.cart.total),
    tipAmount: Math.round(orderInfo.cart.tip),
    cardHolder: `${orderInfo.customer.firstName}/${orderInfo.customer.lastName}`,

    detailedItems: orderInfo.cart.items,
    memo: orderInfo.memo,
    cardPan: orderInfo.card.pan,
    promisedTime: orderInfo.promisedTime.value,
    customerFirstName: orderInfo.customer.firstName,
    customerLastName: orderInfo.customer.lastName,
    customerPhone: orderInfo.customer.phone,
    customerEmail: orderInfo.customer.email,

    authCode: paymentDetails.authcode,
    refTxnID: paymentDetails.transactionid,
  };
};

export const submitOrder: SubmitOrder = async ({
  fetch,
  orderInfo,
  nmiDetails,
  deviceId,
  bridgeApiToken,
}: {
  fetch: SvelteFetch;
  orderInfo: OrderInfo;
  nmiDetails: NmiResponse;
  deviceId: string;
  bridgeApiToken: string;
}) => {
  const orderStateTracker = await OrderStateTracker.instance(orderInfo);
  const bridgeApiSubmitOrderEndpoint = `${getApiBaseUrl(deviceId)}/sale/online/order`;
  const submitOrderPayload = orderInfoToSubmitOrderPayload(
    orderInfo,
    nmiDetails,
  );

  const response = await fetch(bridgeApiSubmitOrderEndpoint, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${bridgeApiToken}`,
    },
    body: JSON.stringify(submitOrderPayload),
  });

  await orderStateTracker.updateState(OrderState.ORDER_SUBMITTED);

  let responseData: unknown;
  try {
    responseData = await response.json();
  } catch (error) {
    logger.error(
      {
        error,
        response,
      },
      "Failed to parse order response from bridge",
    );
  }

  if (!response.ok) {
    await orderStateTracker.updateState(OrderState.ORDER_FAILED);
    throw new CheckoutError({
      name: "BRIDGE_DATA_FORWARDING",
      message: `Forwarding data to bridge returned status ${response.status}: ${response.statusText}`,
      cause: {
        status: response.status,
        statusText: response.statusText,
        responseData,
      },
    });
  }

  await orderStateTracker.updateState(OrderState.ORDER_RECEIVED);

  return responseData as CheckoutResponse;
};
