import { Resend } from "resend";
import { env } from "$env/dynamic/private";
import { getMjmlTemplate } from "$lib/helpers/get-template.ts";
import { publicConfig } from "../../publicConfig.ts";
import { logger } from "$lib/logger/logger.svelte.ts";
import { CheckoutError } from "$lib/services/errors/checkout/CheckoutError.ts";

export const sendEmail = async ({
  to,
  subject,
  templateName,
  variables,
}: {
  to: string;
  subject: string;
  templateName: string;
  variables: Record<string, string | undefined>;
}) => {
  const resend = new Resend(env.RESEND_API_KEY);

  let html = await getMjmlTemplate(templateName);

  // Replace variables in the template
  html = Object.entries(variables).reduce((acc, [key, value]) => {
    return acc.replace(new RegExp(`{{${key}}}`, "g"), value as string);
  }, html);

  // make sure to run 'npm run email-test'
  if (publicConfig.env === "dev") {
    try {
      const { createTransport } = await import("nodemailer");
      const transporter = createTransport({
        host: "localhost",
        port: 1025,
        secure: false,
      });

      await transporter.sendMail({
        from: `"Round 2 POS" <<EMAIL>>`,
        to,
        subject,
        html,
      });
    } catch (error) {
      logger.error(error, "Error sending email:");
      throw error;
    }
  } else {
    const { error: resendError } = await resend.emails.send({
      from: "Round 2 POS <<EMAIL>>",
      to,
      subject,
      html,
    });

    if (resendError) {
      logger.error(resendError, "Failed to send email");
      throw new CheckoutError({
        name: "EMAIL_SENDING_ERROR",
        message: "Failed to send email",
        cause: resendError,
      });
    }

    logger.info({ to }, "Successfully sent email to receipient");
  }
};
