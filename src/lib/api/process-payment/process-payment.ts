import { CheckoutError } from "$lib/services/errors/checkout/CheckoutError.ts";
import type { OrderInfo } from "$lib/types";

export const processPayment = async ({
  nmiToken,
  orderInfo,
  deviceId,
}: {
  nmiToken: string;
  orderInfo: OrderInfo;
  deviceId: string;
}) => {
  const response = await fetch(`/api/${deviceId}/process-payment`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ nmiToken, orderInfo }),
  });

  const result = await response.json();

  if (response.ok) {
    return result;
  } else {
    throw new CheckoutError({
      name: "PAYMENT_PROCESSING",
      message:
        "Payment processing failed. Received bad response from submit payment endpoint",
      cause: result,
    });
  }
};
