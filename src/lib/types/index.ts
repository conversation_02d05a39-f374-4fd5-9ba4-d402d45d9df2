import type { nmiResponseSchema } from "$lib/api/nmi/process-nmi-payment.server";
import type { ModifierListState } from "$lib/components/Modifier/modifier-list-state.svelte";
import type { initPickupTime } from "$lib/components/PickupTime.svelte";
import type { CartState } from "$lib/services/Cart.svelte.ts";
import type z from "zod/v4";

export type CartItem = DetailedItem & {
  cartItemId: string;
  modifierListState?: {
    modifierInputs: ModifierListState["modifierInputs"];
    validationStates: ModifierListState["validationStates"];
  };
};

export type ActiveWindow = {
  open: number;
  close: number;
};

export type ActiveHours = {
  mon: ActiveWindow[];
  tue: ActiveWindow[];
  wed: ActiveWindow[];
  thu: ActiveWindow[];
  fri: ActiveWindow[];
  sat: ActiveWindow[];
  sun: ActiveWindow[];
  /**
   * Christmas
   */
  xmas: ActiveWindow[];
  /**
   * Christmas Eve
   */
  xmasEve: ActiveWindow[];
  /**
   * New Years
   */
  nwYrs: ActiveWindow[];
  /**
   * New Years Eve
   */
  nwYrsEve: ActiveWindow[];
  /**
   * Thanksgiving
   */
  thanks: ActiveWindow[];
  /**
   * Independence Day
   */
  ind: ActiveWindow[];
  /**
   * Labor Day
   */
  labor: ActiveWindow[];
  /**
   * Memorial Day
   */
  memor: ActiveWindow[];
  /**
   * Columbus Day
   */
  colum: ActiveWindow[];
  /**
   * Vertan's Day
   */
  vets: ActiveWindow[];
  /**
   * President's Day
   */
  pres: ActiveWindow[];
  /**
   * Martin Luther King Jr. Day
   */
  mlk: ActiveWindow[];
};

export type StoreInfo = {
  name: string;
  address: string;
  city: string;
  state: string;
  phone: string;
  onlineActive: boolean;
  storeHours: ActiveHours;
};

export type BasicDepartment = {
  department: string; // uuid
  title: string;
  majorGroup?: string;
};

export type BasicItem = {
  item: string; // uuid
  price: number;
  desc: string;
  detailedDesc?: string;
  department: string; // uuid
  count: number;
};

export type DetailedItem = {
  item: string;
  price: number;
  /**
   * Name of the item (not description)
   */
  desc: string;
  detailedDesc?: string;
  modifierDesc?: string | null;
  /**
   * If true, the item's children are labels.
   * If false, the item's children are checkboxes.
   */
  multiModLists: boolean;
  /**
   * Maximum number of modifiers that can be selected (0 for unlimited)
   */
  modMaxSel: number;
  modMinSel: number;
  /**
   * Available stock of item (-1 for unlimited)
   */
  count: number;
  modifiers: DetailedItem[];
  isVisible: boolean;

  // Below are properties that are more state-specific to online ordering
  /**
   * Quantity of item to add to cart if the item is selected.
   */
  qty: number;
  /**
   * Whether to use the quantity of the item.
   */
  selected: boolean;
};

export type CheckoutResponse = {
  saleID?: string;
  tenders?: TenderInfo[];
  transactionId?: string;
  total?: number;
  tipAmount?: number;
  message?: string;
};

export type TenderInfo = {
  media: string;
  amount: number;
  voided: boolean;
  refunded: boolean;
};

export type NmiResponse = z.infer<typeof nmiResponseSchema>;

export type OrderInfo = {
  /**
   * Pickup note set by the customer associated with an order
   */
  memo: string;
  promisedTime: ReturnType<typeof initPickupTime>;
  paymentMethod: PaymentMethod;
  storeInfo: StoreInfo;
  cart: {
    items: CartState["checkoutItems"];
    /**
     * Total in cents
     */
    total: number;
    /**
     * Tip in cents TODO Double check this
     */
    tip: number;
    /**
     * Tax in cents
     */
    tax: number;
    subTotal: number;
    totalItemsCount: number;
  };
  customer: {
    firstName: string;
    lastName: string;
    phone: string;
    email: string | null;
  };
  card: {
    /**
     * Last four card numbers as a string
     */
    pan: string;
  };
};

export type PaymentMethod = "credit-card" | "google-pay" | "apple-pay";

export type ReceiptEmailInfo = OrderInfo & NmiResponse;

export type SubmitOrderPayload = {
  detailedItems: DetailedItem[];
  /**
   * Order total in cents
   */
  amount: number;
  memo?: string;
  tipAmount?: number;
  authCode: string;
  /**
   * masked credit/debit card with last 4 exposed
   */
  cardPan: string;
  /**
   * transaction ID from NMI response
   */
  refTxnID: string;
  /**
   * format: firstName/lastName
   */
  cardHolder: string;
  /**
   * format is military time (24 hour format)
   */
  promisedTime: number;
  customerFirstName: string;
  customerLastName: string;
  customerPhone: string;
  customerEmail: string | null;
};

export type Totals = {
  discountTotal: number;
  gratuityTotal: number;
  subTotal: number;
  takeOutSurchargeTotal: number;
  taxTotal: number;
  taxTotals: number[];
  total: number;
  items: DetailedItem[];
  blockedReason: string[];
};

export type SvelteFetch = {
  (input: RequestInfo | URL, init?: RequestInit): Promise<Response>;
  (
    input: string | URL | globalThis.Request,
    init?: RequestInit,
  ): Promise<Response>;
};
