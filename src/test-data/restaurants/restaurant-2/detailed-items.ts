import { genUuid } from "$lib/helpers/uuid";
import type { DetailedItem } from "$lib/types";

export const detailedItems: DetailedItem[] = [

    {
        item: "bowl01",
        desc: "Build Your Own Bowl",
        price: 1099,
        modMaxSel: 0,
        modMinSel: 0,
        count: 15,
        selected: false,
        qty: 0,
        isVisible: true,
        multiModLists: true,
        modifierDesc: "Choose base and protein",
        modifiers: [
            {
                item: genUuid(),
                desc: "Base",
                price: 0,
                count: 15,
                selected: false,
                qty: 0,
                isVisible: true,
                multiModLists: false,
                modMinSel: 1,
                modMaxSel: 1,
                modifiers: [
                    {
                        item: genUuid(),
                        desc: "White Rice",
                        price: 0,
                        count: 15,
                        selected: false,
                        qty: 0,
                        modifiers: [],
                        isVisible: true,
                        multiModLists: false,
                        modMinSel: 0,
                        modMaxSel: 0,
                    },
                    {
                        item: genUuid(),
                        desc: "Brown Rice",
                        price: 0,
                        count: 15,
                        selected: false,
                        qty: 0,
                        modifiers: [],
                        isVisible: true,
                        multiModLists: false,
                        modMinSel: 0,
                        modMaxSel: 0,
                    },
                ],
            },
            {
                item: genUuid(),
                desc: "Protein",
                price: 0,
                count: 15,
                selected: false,
                qty: 0,
                isVisible: true,
                multiModLists: false,
                modMinSel: 1,
                modMaxSel: 1,
                modifiers: [
                    {
                        item: genUuid(),
                        desc: "Grilled Chicken",
                        price: 0,
                        count: 15,
                        selected: false,
                        qty: 0,
                        modifiers: [],
                        isVisible: true,
                        multiModLists: false,
                        modMinSel: 0,
                        modMaxSel: 0,
                    },
                    {
                        item: genUuid(),
                        desc: "Tofu",
                        price: 0,
                        count: 15,
                        selected: false,
                        qty: 0,
                        modifiers: [],
                        isVisible: true,
                        multiModLists: false,
                        modMinSel: 0,
                        modMaxSel: 0,
                    },
                ],
            },
        ],
    },
    {
        item: "wrap01",
        desc: "Grilled Veggie Wrap",
        price: 899,
        modMaxSel: 0,
        modMinSel: 0,
        count: 10,
        selected: false,
        qty: 0,
        isVisible: true,
        multiModLists: false,
        modifiers: [],
    },
];