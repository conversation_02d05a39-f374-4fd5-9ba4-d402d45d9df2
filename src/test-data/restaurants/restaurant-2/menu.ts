import type { BasicDepartment } from "$lib/types";

import type { BasicItem } from "$lib/types";

export const storeInfo = {
  name: "Fresh Bites",
  address: "456 Fresh Lane",
  city: "Flavor Town",
  state: "FT",
  phone: "************",
  onlineActive: true,
  storeHours: {
    mon: [
      {
        open: 21600000, // 6:00 AM
        close: 50400000, // 14:00 PM
      },
    ],
    tue: [
      {
        open: 21600000, // 6:00 AM
        close: 50400000, // 14:00 PM
      },
    ],
    wed: [
      {
        open: 21600000, // 6:00 AM
        close: 50400000, // 14:00 PM
      },
    ],
    thu: [
      {
        open: 21600000, // 6:00 AM
        close: 50400000, // 14:00 PM
      },
    ],
    fri: [
      {
        open: 21600000, // 6:00 AM
        close: 50400000, // 14:00 PM
      },
    ],
    sat: [
      {
        open: 21600000, // 6:00 AM
        close: 50400000, // 14:00 PM
      },
    ],
    sun: [],
    xmas: [],
    xmasEve: [],
    nwYrs: [],
    nwYrsEve: [],
    thanks: [],
    ind: [],
    labor: [],
    memor: [],
    colum: [],
    vets: [],
    pres: [],
    mlk: [],
  },
};

export const departments: BasicDepartment[] = [
  { department: "1", title: "Wraps" },
  { department: "2", title: "Bowls" },
];

export const items: BasicItem[] = [
  {
    item: "wrap01",
    price: 899,
    desc: "Grilled Veggie Wrap",
    detailedDesc: "Grilled Veggie Wrap with your choice of toppings",
    department: "1",
    count: 1,
  },
  {
    item: "bowl01",
    price: 1099,
    desc: "Build Your Own Bowl",
    detailedDesc: "Build Your Own Bowl with your choice of base and protein",
    department: "2",
    count: 1,
  },
];
