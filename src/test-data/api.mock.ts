import type {
  CheckoutResponse,
  DetailedItem,
  NmiResponse,
  OrderInfo,
  SubmitOrderPayload,
  Totals,
} from "$lib/types";

export const getTestTotals = (
  items: DetailedItem[],
  cartTotal: number,
): Promise<Totals> => {
  return Promise.resolve({
    discountTotal: 0,
    gratuityTotal: 0,
    subTotal: cartTotal,
    takeOutSurchargeTotal: 50,
    taxTotal: cartTotal * 0.08, // 8% mock tax
    taxTotals: [cartTotal * 0.08],
    total: cartTotal * 1.08,
    items: items,
    blockedReason: [],
  });
};

export const processTestPayment = (args: {
  nmiToken: string;
  orderInfo: OrderInfo;
}): Promise<NmiResponse> => {
  console.log("Test payment submitted:", args);
  return Promise.resolve({
    authcode: "123456",
    transactionid: "123456",
    response_code: "100",
    response: "1",
    responsetext: "123456",
    avsresponse: "123456",
    cvvresponse: "123456",
    orderid: "123456",
    type: "sale",
    message: "Test payment successful",
    success: true,
    transactionId: "123456",
  });
};

export const forwardTestData = (
  data: SubmitOrderPayload,
): Promise<CheckoutResponse> => {
  return Promise.resolve({
    saleID: "test-sale-" + Date.now(),
    tenders: [
      {
        media: "Credit Card",
        amount: data.amount,
        voided: false,
        refunded: false,
      },
    ],
    transactionId: "test-transaction-" + Date.now(),
    total: data.amount,
    tipAmount: data.tipAmount || 0,
    message: "Test checkout successful",
  });
};

export const healthCheckTest = (): Promise<{ ok: boolean }> => {
  return Promise.resolve({ ok: true });
};
