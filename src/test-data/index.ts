import type { DetailedItem } from "../lib/types";
import * as restaurant1 from "./restaurants/restaurant-1/menu";
import * as restaurant2 from "./restaurants/restaurant-2/menu";
import * as restaurant1DetailedItems from "./restaurants/restaurant-1/detailed-items";
import * as restaurant2DetailedItems from "./restaurants/restaurant-2/detailed-items";

export const restaurants = [
  {
    device_id: "abcdefg1",
    name: restaurant1.storeInfo.name,
  },
  {
    device_id: "abcdefg2",
    name: restaurant2.storeInfo.name,
  },
] as const;

export const getTestData = (deviceId: string) => {
  if (!deviceId) {
    throw new Error("Restaurant uuid is required");
  }
  if (deviceId === restaurants[0].device_id) {
    const { departments, items, storeInfo } = restaurant1;

    return Promise.resolve({
      departments,
      items,
      storeInfo,
    });
  } else if (deviceId === restaurants[1].device_id) {
    const { departments, items, storeInfo } = restaurant2;

    return Promise.resolve({
      storeInfo,
      departments,
      items,
    });
  }

  return Promise.reject(
    new Error(`Restaurant with uuid ${deviceId} not found`),
  );
};

export const getDetailedTestData = async (
  itemId: string,
): Promise<DetailedItem> => {
  if (!itemId) throw new Error("Item item is required");

  const items = [
    ...restaurant1DetailedItems.detailedItems,
    ...restaurant2DetailedItems.detailedItems,
  ];

  const item = items.find((item) => item.item === itemId);
  if (!item) throw new Error(`Item ${itemId} not found`);

  return Promise.resolve(item);
};
