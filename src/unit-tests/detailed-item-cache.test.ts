import { afterAll, describe, expect, it } from "vitest";
import { DetailedItemCache } from "$lib/services/DetailedItemCache.server.ts";
import { detailedItems } from "../test-data/restaurants/restaurant-2/detailed-items";

const cache = new DetailedItemCache();

afterAll(async () => {
  await cache.clear({ restaurantName: "restaurant-2" });
});

describe("cache should set and get detailed items", () => {
  it("should set and get detailed items", async () => {
    const testItem = detailedItems[0]!;

    await cache.set("restaurant-2", testItem.item, testItem);

    const cachedItems = await cache.get("restaurant-2", testItem.item);
    expect(cachedItems).toEqual(detailedItems[0]);
  });
});
