import { afterEach, describe, expect, it, vi } from "vitest";
import {
  getCurrentDayKey,
  isClosingSoon,
  storeInfoHoursDisplay,
  mapDayToName,
  filterHoliday,
} from "$lib/helpers/store-info";
import type { StoreInfo, ActiveHours, ActiveWindow } from "$lib/types";
import * as time from "$lib/helpers/time";

const HOUR = 60 * 60 * 1000;
const MINUTE = 60 * 1000;

const createEmptyHours = (): ActiveHours => ({
  mon: [],
  tue: [],
  wed: [],
  thu: [],
  fri: [],
  sat: [],
  sun: [],
  xmas: [],
  xmasEve: [],
  nwYrs: [],
  nwYrsEve: [],
  thanks: [],
  ind: [],
  labor: [],
  memor: [],
  colum: [],
  vets: [],
  pres: [],
  mlk: [],
});

const baseInfo = (): StoreInfo => ({
  name: "Test Store",
  address: "123 St",
  city: "City",
  state: "ST",
  phone: "555",
  onlineActive: true,
  storeHours: createEmptyHours(),
});

describe("getCurrentDayKey", () => {
  afterEach(() => {
    vi.useRealTimers();
  });

  it("returns monday key when date is monday", () => {
    vi.useFakeTimers();
    vi.setSystemTime(new Date("2025-05-19T12:00:00Z")); // Monday
    expect(getCurrentDayKey()).toBe("mon");
  });

  it("returns sunday key when date is sunday", () => {
    vi.useFakeTimers();
    vi.setSystemTime(new Date("2025-05-18T12:00:00Z")); // Sunday
    expect(getCurrentDayKey()).toBe("sun");
  });
});

describe("isClosingSoon", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("returns defaults when no hours provided", () => {
    const result = isClosingSoon({ hoursForToday: [] });
    expect(result).toEqual({
      closingSoon: false,
      timeUntilClosingInMs: undefined,
      timeUntilOpeningInMs: undefined,
      currentWindow: null,
    });
  });

  it("detects not closing soon when plenty of time remains", () => {
    vi.spyOn(time, "getLocalTimeInMs").mockReturnValue(10 * HOUR);
    const hours: ActiveWindow[] = [{ open: 9 * HOUR, close: 17 * HOUR }];

    const result = isClosingSoon({ hoursForToday: hours });

    expect(result.closingSoon).toBe(false);
    expect(result.timeUntilClosingInMs).toBe(7 * HOUR);
    expect(result.currentWindow).toEqual(hours[0]);
  });

  it("detects closing soon when window ends within 45 minutes", () => {
    vi.spyOn(time, "getLocalTimeInMs").mockReturnValue(10 * HOUR);
    const hours: ActiveWindow[] = [
      { open: 9 * HOUR, close: 10 * HOUR + 20 * MINUTE },
    ];

    const result = isClosingSoon({ hoursForToday: hours });

    expect(result.closingSoon).toBe(true);
    expect(result.timeUntilClosingInMs).toBe(20 * MINUTE);
    expect(result.currentWindow).toEqual(hours[0]);
  });

  it("returns time until opening when currently closed", () => {
    vi.spyOn(time, "getLocalTimeInMs").mockReturnValue(8 * HOUR);
    const hours: ActiveWindow[] = [{ open: 9 * HOUR, close: 17 * HOUR }];

    const result = isClosingSoon({ hoursForToday: hours });

    expect(result.closingSoon).toBe(false);
    expect(result.timeUntilOpeningInMs).toBe(1 * HOUR);
    expect(result.currentWindow).toBeNull();
  });

  it("handles being closed for the day", () => {
    vi.spyOn(time, "getLocalTimeInMs").mockReturnValue(20 * HOUR);
    const hours: ActiveWindow[] = [{ open: 9 * HOUR, close: 17 * HOUR }];

    const result = isClosingSoon({ hoursForToday: hours });

    expect(result.closingSoon).toBe(false);
    expect(result.currentWindow).toBeNull();
  });
});

describe("storeInfoHoursDisplay", () => {
  afterEach(() => {
    vi.useRealTimers();
  });

  it("returns closed today when no hours", () => {
    const info = baseInfo();
    vi.useFakeTimers();
    vi.setSystemTime(new Date("2025-05-19T12:00:00Z")); // Monday
    expect(storeInfoHoursDisplay(info)).toEqual(["Closed today"]);
  });

  it("formats hours for the current day", () => {
    const info = baseInfo();
    info.storeHours.mon = [{ open: 9 * HOUR, close: 17 * HOUR }];
    vi.useFakeTimers();
    vi.setSystemTime(new Date("2025-05-19T12:00:00Z")); // Monday
    expect(storeInfoHoursDisplay(info)).toEqual(["9:00 AM - 5:00 PM"]);
  });

  it("handles multiple windows", () => {
    const info = baseInfo();
    info.storeHours.mon = [
      { open: 9 * HOUR, close: 11 * HOUR },
      { open: 12 * HOUR, close: 14 * HOUR },
    ];
    vi.useFakeTimers();
    vi.setSystemTime(new Date("2025-05-19T12:00:00Z")); // Monday
    expect(storeInfoHoursDisplay(info)).toEqual([
      "9:00 AM - 11:00 AM",
      "12:00 PM - 2:00 PM",
    ]);
  });
});

describe("mapDayToName", () => {
  it("maps day codes to names", () => {
    expect(mapDayToName("mon")).toBe("Monday");
    expect(mapDayToName("fri")).toBe("Friday");
  });
});

describe("filterHoliday", () => {
  it("passes through weekdays", () => {
    expect(filterHoliday("Monday")).toBe("Monday");
  });

  it("filters out holiday names", () => {
    expect(filterHoliday("Christmas")).toBeNull();
  });
});
