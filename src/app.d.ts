// See https://kit.svelte.dev/docs/types#app
// for information about these interfaces
import type { IBridgeClient } from "$lib/client/bridge/bridge-client.ts";

declare global {
  namespace App {
    // interface Error {}
    interface Locals {
      merchantId: string;
      bridgeClient: IBridgeClient;
      bridgeApiToken?: string;
    }

    // interface PageData {}
    // interface PageState {}
    // interface Platform {}
  }

  declare namespace svelteHTML {
    interface HTMLAttributes {
      /**
       * custom event for Melt UI Dropdown Menu
       * @see
       * https://www.melt-ui.com/docs/builders/dropdown-menu
       */
      "onm-click"?: (event: CustomEvent) => void;
    }
  }
}

export {};
