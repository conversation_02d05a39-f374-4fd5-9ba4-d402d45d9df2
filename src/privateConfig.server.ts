import { env } from "$env/dynamic/private";

const {
  BALENA_AUDIENCE,
  BALENA_AUTH_TOKEN_URL,
  BALENA_CLIENT_ID,
  BALENA_CLIENT_SECRET,
  BALENA_GRANT_TYPE,
  BRIDGE_SERVICE_AUTH_URL,
  BR<PERSON>GE_SERVICE_CLIENT_AUDIENCE,
  BRIDGE_SERVICE_CLIENT_ID,
  BRIDGE_SERVICE_CLIENT_SECRET,
  BRIDGE_SERVICE_JWKS_URI,
  NMI_PRIVATE_KEY,
  NMI_URL,
  REDIS_URI,
  RESTAURANT_CONFIG,
} = env;

export const privateConfig = {
  redisUri: REDIS_URI,
  authUrl: <PERSON><PERSON><PERSON>_SERVICE_AUTH_URL,
  audienceUrl: BRIDGE_SERVICE_CLIENT_AUDIENCE,
  clientId: BRIDGE_SERVICE_CLIENT_ID,
  clientSecret: BRIDGE_SERVICE_CLIENT_SECRET,
  jwksUri: BRIDGE_SERVICE_JW<PERSON>_URI,
  nmiUrl: NMI_URL,
  balenaAuthTokenUrl: <PERSON>LENA_AUTH_TOKEN_URL,
  balenaClientId: BALENA_CLIENT_ID,
  balenaClientSecret: BALENA_CLIENT_SECRET,
  balenaGrantType: BALENA_GRANT_TYPE,
  balenaAudience: BALENA_AUDIENCE,
  nmiTestKey: NMI_PRIVATE_KEY,
  restaurantConfig: RESTAURANT_CONFIG,
};
