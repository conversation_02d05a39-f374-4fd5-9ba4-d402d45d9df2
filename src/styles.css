:root {
  --gray-50: oklch(98.5% 0.001 106.423);
  --gray-100: oklch(97% 0.001 106.424);
  --gray-200: oklch(92.3% 0.003 48.717);
  --gray-300: oklch(86.9% 0.005 56.366);
  --gray-400: oklch(70.9% 0.01 56.259);
  --gray-500: oklch(55.3% 0.013 58.071);
  --gray-600: oklch(44.4% 0.011 73.639);
  --gray-700: oklch(37.4% 0.01 67.558);
  --gray-800: oklch(26.8% 0.007 34.298);
  --gray-900: oklch(21.6% 0.006 56.043);
  --gray-950: oklch(14.7% 0.004 49.25);

  --green-500: #2f9e44;

  --red-50: #fdf0f0;
  --red-75: rgba(217, 28, 28, 0.1);
  --red-100: #fddddd;
  --red-200: #fbc1c1;
  --red-300: #f89696;
  --red-400: #f15a5a;
  --red-500: #d91c1c;
  --red-600: #a02323;
  --red-700: #8d1d1d;
  --red-800: #731c1c;
  --red-900: #601d1d;
  --red-950: #350a0a;

  --blue-50: oklch(96.63% 0.02 250.84);
  --blue-100: oklch(92% 0.04 257.92);
  --blue-200: oklch(86.39% 0.07 255.34);
  --blue-300: oklch(77.9% 0.12 253.11);
  --blue-400: oklch(67.45% 0.17 256.37);
  --blue-500: oklch(57.15% 0.24 261.56);
  --blue-600: oklch(51.63% 0.27 263.73);
  --blue-700: oklch(45.22% 0.25 264.43);
  --blue-800: oklch(39.14% 0.21 265.27);
  --blue-900: oklch(34.83% 0.16 265.82);
  --blue-950: oklch(26.1% 0.1 268.22);

  --indigo-50: oklch(96.2% 0.018 272.314);
  --indigo-100: oklch(93% 0.034 272.788);
  --indigo-200: oklch(87% 0.065 274.039);
  --indigo-300: oklch(78.5% 0.115 274.713);
  --indigo-400: oklch(67.3% 0.182 276.935);
  --indigo-500: oklch(58.5% 0.233 277.117);
  --indigo-600: oklch(51.1% 0.262 276.966);
  --indigo-700: oklch(45.7% 0.24 277.023);
  --indigo-800: oklch(39.8% 0.195 277.366);
  --indigo-900: oklch(35.9% 0.144 278.697);
  --indigo-950: oklch(25.7% 0.09 281.288);

  --green-75: rgba(47, 158, 68, 0.1);

  --main-bg-color: var(--gray-50);

  /*--primary-color: #ec1c24; Round 2 POS color */
  --primary-color: var(--indigo-600);
  --primary-color-light: var(--indigo-50);
  --primary-color-hover: var(--indigo-700);
  --primary-color-active: var(--indigo-800);

  --font-xs: 0.75rem;
  --font-sm: 0.875rem;
  --font-md: 1rem;
  --font-lg: 1.25rem;
  --font-xl: 1.5rem;
  --font-2xl: 2rem;
  --font-4xl: 3rem;

  --line-height-lg: 2;

  --padding-xs: 0.25rem;
  --padding-sm: 0.5rem;
  --padding-md: 1rem;
  --padding-lg: 1.5rem;
  --padding-xl: 2rem;

  --gap-xs: 0.25rem;
  --gap-sm: 0.5rem;
  --gap-md: 1rem;

  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-full: 9999px;

  --min-header-height: 200px;
  --min-header-height-mobile: 140px;
  --sticky-header-height: 100px;
  --footer-height: 5rem;

  --large-screen-cart-width: 400px;

  --width-sm: 24px;
  --height-sm: 24px;

  --margin-xs: 0.25rem;
  --margin-sm: 0.5rem;
  --margin-md: 1rem;
  --margin-lg: 1.5rem;
  --margin-xl: 2rem;

  --header-font: "Poppins", sans-serif;
  --content-font: "Inter", sans-serif;

  --shadow-color: 0deg 0% 72%;
  --shadow-elevation-low: 0.3px 1px 1px hsl(var(--shadow-color) / 0.35),
    0.4px 1.4px 1.4px -1.7px hsl(var(--shadow-color) / 0.29),
    1px 3.5px 3.5px -3.5px hsl(var(--shadow-color) / 0.22);
  --shadow-elevation-medium: 0.3px 1px 1px hsl(var(--shadow-color) / 0.3),
    0.5px 1.9px 1.9px -0.9px hsl(var(--shadow-color) / 0.26),
    1.1px 4px 4px -1.7px hsl(var(--shadow-color) / 0.23),
    2.4px 8.6px 8.7px -2.6px hsl(var(--shadow-color) / 0.19),
    4.9px 17.4px 17.6px -3.5px hsl(var(--shadow-color) / 0.16);
  --shadow-elevation-high: 0.3px 1px 1px hsl(var(--shadow-color) / 0.31),
    0.8px 2.9px 2.9px -0.4px hsl(var(--shadow-color) / 0.29),
    1.5px 5.2px 5.3px -0.9px hsl(var(--shadow-color) / 0.27),
    2.4px 8.7px 8.8px -1.3px hsl(var(--shadow-color) / 0.25),
    4px 14.2px 14.4px -1.7px hsl(var(--shadow-color) / 0.23),
    6.3px 22.7px 23px -2.2px hsl(var(--shadow-color) / 0.21),
    9.7px 34.9px 35.3px -2.6px hsl(var(--shadow-color) / 0.19),
    14.5px 51.7px 52.4px -3.1px hsl(var(--shadow-color) / 0.17),
    20.7px 74px 74.9px -3.5px hsl(var(--shadow-color) / 0.15);

  --screen-sm: 640px;

  scroll-behavior: smooth;

  color: var(--gray-950);

  font-family: var(--content-font), sans-serif;
}

body {
  color: var(--gray-950);
  font-family: var(--content-font);
}

h1 {
  font-weight: 700;
  font-size: var(--font-xl);
  font-family: var(--header-font);
}

h2 {
  font-size: var(--font-lg);
  font-family: var(--header-font);
}

h3 {
  font-size: var(--font-md);
  font-family: var(--header-font);
}

h4 {
  font-family: var(--header-font);
}

h5 {
  font-family: var(--header-font);
}

h6 {
  font-family: var(--header-font);
}

@media (min-width: 640px) {
  h1 {
    font-size: var(--font-2xl);
  }

  h2 {
    font-size: var(--font-xl);
  }
}

button {
  cursor: pointer;
  color: inherit;
  font-family: inherit;
}

button:disabled {
  cursor: not-allowed;
}

input:not([type="checkbox"]) {
  transition: border-color 0.2s ease-in-out;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  padding: var(--padding-md);
  width: 100%;
  font-size: var(--font-sm);
}

#ccExp input,
#ccNumber input {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

input:focus {
  outline: none;
  border-color: var(--primary-color);
}

label {
  margin-bottom: 0.5rem;
  color: var(--gray-800);
  font-size: var(--font-md);
}

.overlay {
  position: fixed;
  opacity: 50%;
  inset: 0;
  background-color: black;
}

.sr-only {
  position: absolute;
  margin: -1px;
  padding: 0;
  width: 1px;
  height: 1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border-width: 0;
  white-space: nowrap;
}

/* Initially hide the link off-screen */
.skip-link {
  position: absolute;
  top: -100px;
  left: -100px;
  opacity: 0;
  transition:
    top 0.3s ease,
    left 0.3s ease;
  border-radius: 4px;
  background: #001aff;
  padding: 10px;
  color: #fff;
  font-size: 16px;
  text-decoration: none;
}

/* Make the link visible when focused via keyboard navigation */
.skip-link:focus {
  top: 10px;
  left: 10px;
  opacity: 1;
}

/* this is purely for development purposes only */
.caution-tape {
  border: 10px solid pink;
  border-image: repeating-linear-gradient(
      -55deg,
      #000,
      #000 20px,
      #ffb101 20px,
      #ffb101 40px
    )
    10;
}

.required-star {
  color: var(--red-500);

  &::after {
    content: "*";
  }
}

@media (min-width: 1024px) {
  .mobile-view {
    display: none;
  }
}

@media (max-width: 1023px) {
  .desktop-only {
    display: none;
  }
}

/**
Naming??? Should probably just make a component in the future
**/
.button--primary-lg {
  transition: transform 50ms ease-out;
  border-radius: var(--radius-md);
  background-color: var(--primary-color);
  padding: var(--padding-md);
  width: 100%;
  color: white;
  font-weight: 700;
  font-size: var(--font-lg);

  &:disabled {
    cursor: not-allowed;
    background-color: var(--gray-200);
    color: var(--gray-600);
  }

  &:has(p) {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &:hover:not(:disabled) {
    background-color: var(--primary-color-hover);
  }

  &:active:not(:disabled) {
    transform: scale(0.98);
    background-color: var(--primary-color-active);
  }
}

.button--secondary {
  transition: background-color 0.2s ease;
  border: 1px solid var(--blue-400);
  border-radius: var(--radius-sm);
  background-color: var(--primary-color-light);
  padding: var(--padding-sm);
  width: 100%;
  color: var(--blue-950);
  font-weight: 500;
  font-size: var(--font-sm);

  &:disabled {
    cursor: not-allowed;
    background-color: var(--gray-200);
    color: var(--gray-600);
  }
}

/**
This may be overkill, but trying to copy the example we're using for now.
**/
::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

/* https://css-loaders.com/dots-bars/ */
.loader {
  width: 40px;
  height: 20px;
  --c: no-repeat radial-gradient(farthest-side, #000 93%, #0000);
  position: relative;
  clip-path: inset(-200% -100% 0 0);
  animation: l6-0 1.5s linear infinite;
  background:
    var(--c) 0 0,
    var(--c) 50% 0;
  background-size: 8px 8px;
}

.loader:before {
  position: absolute;
  top: 0;
  left: -16px;
  animation:
    l6-1 1.5s linear infinite,
    l6-2 0.5s cubic-bezier(0, 200, 0.8, 200) infinite;
  background: #000;
  width: 8px;
  height: 12px;
  content: "";
}

.loader:after {
  position: absolute;
  animation: l6-3 1.5s linear infinite;
  inset: 0 0 auto auto;
  border-radius: 50%;
  background: #000;
  width: 8px;
  height: 8px;
  content: "";
}

@keyframes l6-0 {
  0%,
  30% {
    background-position:
      0 0,
      50% 0;
  }
  33% {
    background-position:
      0 100%,
      50% 0;
  }
  41%,
  63% {
    background-position:
      0 0,
      50% 0;
  }
  66% {
    background-position:
      0 0,
      50% 100%;
  }
  74%,
  100% {
    background-position:
      0 0,
      50% 0;
  }
}

@keyframes l6-1 {
  90% {
    transform: translateY(0);
  }
  95% {
    transform: translateY(15px);
  }
  100% {
    left: calc(100% - 8px);
    transform: translateY(15px);
  }
}

@keyframes l6-2 {
  100% {
    top: -0.1px;
  }
}

@keyframes l6-3 {
  0%,
  80%,
  100% {
    transform: translate(0);
  }
  90% {
    transform: translate(26px);
  }
}

@keyframes fadeIn {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
