# Build stage
FROM node:20-alpine as builder

# Install doppler CLI
RUN wget -q -t3 'https://packages.doppler.com/public/cli/rsa.8004D9FF50437357.key' -O /etc/apk/keys/<EMAIL> && \
    echo 'https://packages.doppler.com/public/cli/alpine/any-version/main' | tee -a /etc/apk/repositories && \
    apk add doppler

# Accept build arg for Doppler token
ARG DOPPLER_TOKEN
ENV DOPPLER_TOKEN=$DOPPLER_TOKEN

ARG DOPPLER_CONFIG
ENV DOPPLER_CONFIG=$DOPPLER_CONFIG

WORKDIR /app

COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

# Production stage
FROM node:20-alpine
WORKDIR /app

# Install doppler CLI
RUN wget -q -t3 'https://packages.doppler.com/public/cli/rsa.8004D9FF50437357.key' -O /etc/apk/keys/<EMAIL> && \
    echo 'https://packages.doppler.com/public/cli/alpine/any-version/main' | tee -a /etc/apk/repositories && \
    apk add doppler

# Accept build arg for Doppler token
ARG DOPPLER_TOKEN
ENV DOPPLER_TOKEN=$DOPPLER_TOKEN

# Add DOPPLER_CONFIG to production stage
ARG DOPPLER_CONFIG
ENV DOPPLER_CONFIG=$DOPPLER_CONFIG

RUN echo DOPPLERCONFIG${DOPPLER_CONFIG}

# Copy built files and package files
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/build ./build
COPY --from=builder /app/src/test-data ./src/test-data/

# Install production dependencies only
RUN npm ci --production

EXPOSE 3000

# Use shell form to debug
CMD ["npm", "run", "start"]
