apiVersion: v1
kind: Secret
metadata:
  name: staging-basic-auth
type: Opaque
data:
  # Generated with `echo -n "$(htpasswd -nbB USER_NAME 'PASSWORD')" | base64 --wrap 0`.
  auth: bmF0aWxpIG5vcnRoOiQyeSQwNSRoSHo5cmM5Q1U4ZGI4c3h1djAwNy8uVmRndWRDUjJvYkZVUzlEMjQ3QzJGNm1MYmk0TGNObQ==
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: online-ordering-staging-ingress
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-body-size: "4096m"
    # This is the basic auth for the ingress, since we don't want outsiders to
    # see our staging environment.
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: staging-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: "Restricted Access"

    # Proxy headers
    nginx.ingress.kubernetes.io/proxy-set-headers: |
      X-Real-IP $remote_addr
      X-Forwarded-For $proxy_add_x_forwarded_for

    # Force HTTPS
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - order.staging.round2pos.com
      secretName: online-ordering-staging-round2pos-tls
  rules:
    - host: order.staging.round2pos.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: online-ordering
                port:
                  number: 80
