apiVersion: apps/v1
kind: Deployment
metadata:
  name: online-ordering
spec:
  replicas: 3
  selector:
    matchLabels:
      app: online-ordering
  template:
    metadata:
      labels:
        app: online-ordering
    spec:
      containers:
        - name: online-ordering
          image: <IMAGE>
          envFrom:
            - secretRef:
                name: doppler-staging-token
          ports:
            - containerPort: 3000
          resources:
            limits:
              memory: "1Gi"
              cpu: "500m"
            requests:
              memory: "512Mi"
              cpu: "100m"
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: online-ordering
spec:
  ports:
    - port: 80
      targetPort: 3000
  selector:
    app: online-ordering
