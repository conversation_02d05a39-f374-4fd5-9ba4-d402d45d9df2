apiVersion: v1
kind: Secret
metadata:
  name: production-basic-auth
type: Opaque
data:
  # Generated with `echo -n "$(htpasswd -nbB USER 'PASS')" | base64 --wrap 0`.
  auth: cHJvZHVjdGlvbjokMnkkMDUkR2xtbUFHbzRGSVlvQS9TOElNL3VRT0x3QVZiU3M5OEVXLmtMVVJ2Mlk0a3UxMU1UMVMySGU=
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: online-ordering-production-ingress
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-body-size: "4096m"
    # This is the basic auth for the ingress, since we don't want outsiders to
    # see our production environment.
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: production-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: "Restricted Access"

    # Proxy headers
    nginx.ingress.kubernetes.io/proxy-set-headers: |
      X-Real-IP $remote_addr
      X-Forwarded-For $proxy_add_x_forwarded_for

    # Force HTTPS
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - order.round2pos.com
      secretName: online-ordering-production-round2pos-tls
  rules:
    - host: order.round2pos.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: online-ordering
                port:
                  number: 80
