apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis
spec:
  serviceName: redis
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
        - name: redis
          image: redis:7.2.4
          command: ["sh", "-c", "redis-server /etc/redis/redis.conf --requirepass ${REDIS_PASSWORD}"]
          env:
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: redis-auth
                  key: password
          volumeMounts:
            - name: data
              mountPath: /data
            - name: conf
              mountPath: /etc/redis/redis.conf
              subPath: redis.conf
          resources:
            requests:
              cpu: 100m
              memory: 100Mi
            limits:
              cpu: 500m
              memory: 500Mi
          livenessProbe:
            exec: { command: ["sh", "-c", "redis-cli -a ${REDIS_PASSWORD} ping"] }
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            tcpSocket: { port: 6379 }
            initialDelaySeconds: 5
            periodSeconds: 10
      volumes:
        - name: conf
          configMap:
            name: redis-conf
  volumeClaimTemplates:
    - metadata:
        name: data
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 1Gi
