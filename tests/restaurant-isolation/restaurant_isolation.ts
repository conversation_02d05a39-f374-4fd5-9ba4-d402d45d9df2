import type { BasicDepartment, BasicItem, StoreInfo } from "$lib/types";
import { expect, type Page } from "@playwright/test";
import { config } from "../global";

export const navigateToRestaurant = async (
  page: Page,
  restaurantId: string,
) => {
  const restaurantUrl = config(restaurantId).baseUrl;
  await page.goto(restaurantUrl);

  await expect(page.getByRole("region", { name: "items" })).toBeVisible();

  return page;
};

export const verifyRestaurantInfo = async (
  page: Page,
  expectedData: StoreInfo,
) => {
  await expect(page.getByText(expectedData.name)).toBeVisible();

  const header = page.locator("header");

  await expect(header.getByText(expectedData.name)).toBeVisible();
  await expect(header.getByText(expectedData.address)).toBeVisible();
};

export const verifyDepartments = async (
  page: Page,
  expectedDepartments: BasicDepartment[],
) => {
  for (const department of expectedDepartments) {
    await expect(
      page.getByRole("link", { name: department.title, exact: true }),
    ).toBeVisible();
  }
};

export const verifyMenuItems = async (
  page: Page,
  expectedItems: BasicItem[],
) => {
  const itemsRegion = page.getByRole("region", { name: "items" });
  await expect(itemsRegion).toBeVisible();

  for (const itemDesc of expectedItems) {
    const itemButton = itemsRegion
      .getByRole("button")
      .getByRole("heading", { name: itemDesc.desc, exact: true });
    await expect(itemButton).toBeVisible();
  }
};
