import { expect, test } from "@playwright/test";
import * as restaurant1 from "../../src/test-data/restaurants/restaurant-1/menu";
import * as restaurant2 from "../../src/test-data/restaurants/restaurant-2/menu";
import {
  navigateToRestaurant,
  verifyDepartments,
  verifyMenuItems,
  verifyRestaurantInfo,
} from "./restaurant_isolation";

const restaurantData = {
  "1": {
    storeInfo: restaurant1.storeInfo,
    departments: restaurant1.departments,
    items: restaurant1.items,
  },
  "2": {
    storeInfo: restaurant2.storeInfo,
    departments: restaurant2.departments,
    items: restaurant2.items,
  },
};

test.describe("Restaurant Isolation", () => {
  test.describe.configure({ mode: "parallel" });

  test("should load restaurant-1 with correct data", async ({ page }) => {
    const restaurant = "1";
    const data = restaurantData[restaurant];

    await navigateToRestaurant(page, restaurant);
    await verifyRestaurantInfo(page, data.storeInfo);
    await verifyDepartments(page, data.departments);
    await verifyMenuItems(page, data.items);
  });

  test("should load restaurant-2 with correct data", async ({ page }) => {
    const secondRestaurant = "2";
    const data = restaurantData[secondRestaurant];

    await navigateToRestaurant(page, secondRestaurant);
    await verifyRestaurantInfo(page, data.storeInfo);
    await verifyDepartments(page, data.departments);
    await verifyMenuItems(page, data.items);
  });

  test("should maintain isolation between restaurants", async ({ page }) => {
    const restaurant = "1";
    const data = restaurantData[restaurant];

    await navigateToRestaurant(page, restaurant);
    await verifyRestaurantInfo(page, data.storeInfo);
    await verifyDepartments(page, data.departments);
    await verifyMenuItems(page, data.items);

    const secondRestaurant = "2";
    const secondData = restaurantData[secondRestaurant];

    await navigateToRestaurant(page, secondRestaurant);
    await verifyRestaurantInfo(page, secondData.storeInfo);
    await verifyDepartments(page, secondData.departments);
    await verifyMenuItems(page, secondData.items);

    await expect(page.getByText(data.storeInfo.name)).not.toBeVisible();
    /**
     * https://stackoverflow.com/questions/47144187/can-you-write-async-tests-that-expect-tothrow
     */
    await expect(
      async () => await verifyMenuItems(page, data.items),
    ).rejects.toThrow();
  });
});
