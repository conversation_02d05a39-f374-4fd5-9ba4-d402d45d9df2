import AxeBuilder from "@axe-core/playwright";
import { expect, test } from "@playwright/test";
import { config } from "./global";
import {
  addWithModifier,
  checkItemInCart,
  getCart,
  getMoreSelectionsButton,
  openItemDialog,
} from "./items";

test.describe("item", () => {
  const itemName = "Steak Frites";

  test.beforeEach(async ({ page }) => {
    await page.goto(config("1").baseUrl);
  });

  test('should display "Out of Stock" when item count is 0', async ({
    page,
  }) => {
    const itemName = "Out of stock item";

    const outOfStockItem = page.getByRole("button", { name: itemName });
    await expect(outOfStockItem).toBeVisible();

    const outOfStockBanner = outOfStockItem.locator(".out-of-stock-badge");
    await expect(outOfStockBanner).toBeVisible();
  });

  test("should open a dialog when clicked", async ({ page }) => {
    const dialog = await openItemDialog(page, itemName);
    const heading = dialog.getByRole("heading", { name: itemName });

    await expect(heading).toBeVisible();
  });

  test("should not introduce accessibility errors when opening dialog", async ({
    page,
  }) => {
    await page.emulateMedia({ reducedMotion: "reduce" });

    await openItemDialog(page, itemName);

    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test("should not show choose details if item doesn't have child modifiers", async ({
    page,
  }) => {
    const itemName = "Steak Frites";

    await openItemDialog(page, itemName);

    await page.getByRole("checkbox", { name: "Rare", exact: true }).click();
    await expect(getMoreSelectionsButton(page)).not.toBeVisible();
  });

  test("should be able to add an item to cart with no modifiers", async ({
    page,
    isMobile,
  }) => {
    const itemName = "Item with no modifiers";

    await addWithModifier({
      page,
      itemName,
      modifiersToSelect: [],
    });

    await checkItemInCart({ page, itemName, modifiers: [], isMobile });
  });

  test("should be able to add an item to cart with non-required modifiers", async ({
    page,
    isMobile,
  }) => {
    const itemName = "Caesar Salad";

    const dialog = await openItemDialog(page, itemName);

    await expect(
      dialog.getByRole("checkbox", { name: "Bacon Bits" }),
    ).toBeVisible();

    await dialog.getByRole("button", { name: "Add to Cart" }).click();

    await checkItemInCart({ page, itemName, modifiers: [], isMobile });
  });

  test("should be able to add an item to cart with single nested invalid modifier", async ({
    page,
    isMobile,
  }) => {
    const itemName = "Chicken Alfredo";

    // Add several of the same item to avoid false positives.
    await addWithModifier({
      page,
      itemName,
      modifiersToSelect: ["Fettuccine"],
    });
    await addWithModifier({ page, itemName, modifiersToSelect: ["Penne"] });
    await addWithModifier({
      page,
      itemName,
      modifiersToSelect: ["Gluten-Free"],
    });

    const cart = await getCart(page, isMobile);

    const cartItems = cart.getByRole("listitem");
    const itemText = page.locator(`[data-testId="cart-item-${itemName}"]`);
    const penneModifier = page.getByText("Penne");

    const chickenAlfredoWithPenneItem = cartItems
      .filter({ has: itemText })
      .filter({ has: penneModifier });

    await expect(chickenAlfredoWithPenneItem).toBeVisible();
  });

  test("should be able to add an item to cart with mixed complex modifiers", async ({
    page,
    isMobile,
  }) => {
    const itemName = "Cheeseburger";

    const dialog = await openItemDialog(page, itemName);

    // TODO These locators should involve more narrowing, but accessibility on modifiers needs to be improved first.
    await page.getByRole("checkbox", { name: "Extra Cheese" }).click();
    await getMoreSelectionsButton(page).click();
    await page.getByRole("checkbox", { name: "Double Cheese" }).click();
    await page.getByRole("button", { name: "Confirm choice" }).click();

    await page.getByRole("checkbox", { name: "Brioche Bun" }).click();
    await page.getByRole("checkbox", { name: "Veggie Patty" }).click();
    await page.getByRole("checkbox", { name: "Onion Rings" }).click();
    await page.getByRole("checkbox", { name: "Medium Rare" }).click();

    await dialog.getByRole("button", { name: "Add to Cart" }).click();

    const cart = await getCart(page, isMobile);

    const cartItem = cart.getByRole("listitem").filter({ hasText: itemName });

    await expect(cartItem).toContainText("Brioche Bun");
    await expect(cartItem).toContainText("Veggie Patty");
    await expect(cartItem).toContainText("Onion Rings");
    await expect(cartItem).toContainText("Medium Rare");
    await expect(cartItem).toContainText("Double Cheese");
  });
});
