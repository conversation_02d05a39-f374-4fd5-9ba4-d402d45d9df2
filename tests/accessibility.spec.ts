import AxeBuilder from "@axe-core/playwright";
import { expect, test } from "@playwright/test";
// import { getData } from "../src/test-data";
import { config } from "./global";

// Extend the Window interface to include mockScreenReader
declare global {
  interface Window {
    mockScreenReader: {
      announcements: string[];
      announce: (message: string) => void;
    };
  }
}

test.describe("homepage", () => {
  test("should not have any automatically detectable accessibility issues", async ({
    page,
  }) => {
    await page.goto(config("1").baseUrl);

    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });
});

test("should not have any automatically detectable WCAG A or AA violations", async ({
  page,
}) => {
  await page.goto(config("1").baseUrl);

  const accessibilityScanResults = await new AxeBuilder({ page })
    .withTags(["wcag2a", "wcag2aa", "wcag21a", "wcag21aa"])
    .analyze();

  expect(accessibilityScanResults.violations).toEqual([]);
});

// test tabbing through the page to ensure that all interactive elements are reachable
// test("should be able to tab through all interactive elements", async ({
//   page,
// }) => {
//   await page.goto(config.baseUrl);
//   await page.getByRole("heading", { name: "Categories" }).click();
//   await page.locator("body").press("Tab");
//   await page.getByRole("link", { name: "Appetizers" }).press("Tab");
//   await page.getByRole("link", { name: "Pizzas" }).press("Tab");
//   await page.getByRole("link", { name: "Main Courses" }).press("Tab");
//   await page.getByRole("link", { name: "Desserts" }).press("Tab");
//   await page.getByRole("link", { name: "Beverages" }).press("Tab");
//   await page.getByRole("link", { name: "Salads" }).press("Enter");
//   await page.locator("body").press("Tab");
//   await page
//     .getByRole("button", { name: "Caesar Salad Crisp romaine" })
//     .press("Tab");
//   await page
//     .getByRole("button", { name: "Cheeseburger Juicy beef patty" })
//     .press("Tab");
//   await page
//     .getByRole("button", { name: "Chicken Caesar Wrap Grilled" })
//     .press("Tab");
//   await page
//     .getByRole("button", { name: "French Onion Soup Classic" })
//     .press("Tab");
//   await page.getByRole("heading", { name: "Categories" }).click();
//   await page.locator("body").press("Tab");
//   await page.getByRole("link", { name: "Appetizers" }).press("Tab");
//   await page.getByRole("link", { name: "Pizzas" }).press("Tab");
//   await page.getByRole("link", { name: "Main Courses" }).press("Tab");
//   await page.getByRole("link", { name: "Desserts" }).press("Tab");
//   await page.getByRole("link", { name: "Beverages" }).press("Tab");
//   await page.getByRole("link", { name: "Salads" }).press("Tab");
//   await page.getByRole("link", { name: "Sandwiches" }).press("Tab");
//   await page.getByRole("link", { name: "Soups" }).press("Tab");
//   await page.getByRole("link", { name: "Seafood" }).press("Tab");
//   await page.getByRole("button", { name: "Dropdown" }).press("Enter");
//   await page.getByRole("menuitem", { name: "Appetizers" }).press("ArrowDown");
//   await page.getByRole("menuitem", { name: "Pizzas" }).press("ArrowDown");
//   await page.getByRole("menuitem", { name: "Main Courses" }).press("ArrowDown");
//   await page.getByRole("menuitem", { name: "Desserts" }).press("ArrowDown");
//   await page.getByRole("menuitem", { name: "Beverages" }).press("ArrowDown");
//   await page.getByRole("menuitem", { name: "Salads" }).press("ArrowDown");
//   await page.getByRole("menuitem", { name: "Sandwiches" }).press("ArrowDown");
//   await page.getByRole("menuitem", { name: "Soups" }).press("ArrowDown");
//   await page.getByRole("menuitem", { name: "Seafood" }).press("Enter");
//   await page.locator("body").press("Tab");
//   await page
//     .getByRole("button", { name: "Fish and Chips Beer-battered" })
//     .press("Tab");
//   await page.getByRole("heading", { name: "Categories" }).click();
//   await page.locator("body").press("Tab");
//   await page.getByRole("link", { name: "Appetizers" }).press("Tab");
//   await page.getByRole("link", { name: "Pizzas" }).press("Tab");
//   await page.getByRole("link", { name: "Main Courses" }).press("Tab");
//   await page.getByRole("link", { name: "Desserts" }).press("Tab");
//   await page.getByRole("link", { name: "Beverages" }).press("Tab");
//   await page.getByRole("link", { name: "Salads" }).press("Tab");
//   await page.getByRole("link", { name: "Sandwiches" }).press("Tab");
//   await page.getByRole("link", { name: "Soups" }).press("Tab");
//   await page.getByRole("link", { name: "Seafood" }).press("Tab");
//   await page.getByRole("button", { name: "Dropdown" }).press("Enter");
//   await page.getByRole("menuitem", { name: "Appetizers" }).press("Tab");
//   await page.getByLabel("Toggle search").press("Enter");
//   await page.getByPlaceholder("Search...").press("Escape");
//   await page.getByPlaceholder("Search...").press("Tab");
//   await page.getByLabel("Close search").press("Enter");
//   await page.locator("body").press("Shift+Tab");
//   await page.getByRole("button", { name: "Dropdown" }).press("Enter");
//   await page.getByRole("menuitem", { name: "Appetizers" }).press("ArrowDown");
//   await page.getByRole("menuitem", { name: "Pizzas" }).press("ArrowDown");
//   await page.getByRole("menuitem", { name: "Main Courses" }).press("ArrowDown");
//   await page.getByRole("menuitem", { name: "Desserts" }).press("ArrowDown");
//   await page.getByRole("menuitem", { name: "Beverages" }).press("ArrowDown");
//   await page.getByRole("menuitem", { name: "Salads" }).press("ArrowDown");
//   await page.getByRole("menuitem", { name: "Sandwiches" }).press("ArrowDown");
//   await page.getByRole("menuitem", { name: "Soups" }).press("ArrowDown");
//   await page.getByRole("menuitem", { name: "Seafood" }).press("ArrowDown");
//   await page.getByRole("menuitem", { name: "Appetizers" }).press("Escape");
//   await page.locator("body").press("Shift+Tab");
// });

// test("screen reader should let the user know when items are added to the cart", async ({
//   page,
// }) => {
//   await page.goto(config.baseUrl);

//   // Inject the mock screen reader into the page
//   await page.evaluate(() => {
//     window.mockScreenReader = {
//       announcements: [],
//       announce: function (message: string) {
//         this.announcements.push(message);
//         console.log("Screen reader announcement:", message);
//       },
//     };
//   });

//   // Mock screen reader
//   await page.evaluate(() => {
//     const observer = new MutationObserver((mutations) => {
//       mutations.forEach((mutation) => {
//         if (
//           mutation.type === "childList" &&
//           mutation.target instanceof Element &&
//           mutation.target.getAttribute("aria-live") === "polite"
//         ) {
//           const newText = mutation.target.textContent;
//           if (newText !== null) {
//             window.mockScreenReader.announce(newText);
//           }
//         }
//       });
//     });

//     const ariaLiveRegions = document.querySelectorAll("[aria-live]");
//     ariaLiveRegions.forEach((region) => {
//       observer.observe(region, { childList: true, subtree: true });
//     });
//   });

//   // Interact with the page and add items to the cart
//   const mozzarellaSticks = page.getByRole("button", {
//     name: "Mozzarella Sticks Crispy",
//   });
//   await mozzarellaSticks.scrollIntoViewIfNeeded();
//   await mozzarellaSticks.click();
//   await page.getByRole("button", { name: "Add to cart $" }).click();

//   await page.waitForTimeout(config.timeouts.short);

//   const garlicBread = page.getByRole("button", {
//     name: "Garlic Bread Toasted bread",
//   });
//   await garlicBread.scrollIntoViewIfNeeded();
//   await garlicBread.click();
//   await page.getByRole("button", { name: "Add to cart $" }).click();

//   await page.waitForTimeout(config.timeouts.short);

//   const pizza = page.getByRole("button", { name: "Margherita Pizza Classic" });
//   await pizza.scrollIntoViewIfNeeded();
//   await pizza.click();
//   await page.getByRole("button", { name: "Add to cart $" }).click();

//   // Check if the screen reader announcements were made
//   const announcements = await page.evaluate(
//     () => window.mockScreenReader.announcements,
//   );

//   expect(announcements[0]).toContain("Cart updated");
//   expect(announcements[0]).toContain("Mozzarella Sticks");

//   expect(announcements[1]).toContain("Cart updated");
//   expect(announcements[1]).toContain("Mozzarella Sticks");

//   expect(announcements[2]).toContain("Cart updated");
//   expect(announcements[2]).toContain("Mozzarella Sticks");
//   expect(announcements[2]).toContain("Garlic Bread");
//   expect(announcements[2]).toContain("Margherita Pizza");
//   expect(announcements[2]).toContain("3 items");
// });
