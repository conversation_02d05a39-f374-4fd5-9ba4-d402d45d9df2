import { devices, /* expect, */ test } from "@playwright/test";
import { config } from "./global";
test.use({
  ...devices["Pixel 7"],
});

test.describe("mobile devices test", () => {
  test("should be able to load the page", async ({ page }) => {
    // assuming you have CRM running on port 5173
    await page.goto(config("1").baseUrl);
  });

  // test("should be able to add an item to the cart and checkout", async ({
  //   page,
  // }) => {
  //   // assuming you have CRM running on port 5173
  //   await page.goto(config.baseUrl);
  //   await page.waitForLoadState("domcontentloaded");

  //   await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));

  //   await page.getByRole("button", { name: "Open Categories" }).tap();

  //   await page.waitForTimeout(config.timeouts.short);
  //   await page.getByLabel("Categories").getByText("Appetizers").tap();

  //   await page.waitForTimeout(config.timeouts.short);

  //   const shrimp = page.getByRole("button", {
  //     name: "Shrimp Cocktail Chilled",
  //   });
  //   await shrimp.tap();
  //   await page.waitForTimeout(config.timeouts.short);

  //   await page.getByLabel("Increase quantity by").tap();
  //   await page.getByText("Add to cart $").tap();

  //   await page
  //     .getByRole("button", { name: "2 item(s) in cart View Cart $" })
  //     .click();
  //   await page.getByLabel("", { exact: true }).getByText("Checkout").tap();

  //   await page.waitForTimeout(config.timeouts.short);

  //   await page.getByRole("button", { name: "Continue" }).tap();
  //   await page.getByPlaceholder("Name").fill("Playwright!");
  //   await page.getByPlaceholder("Email").fill("<EMAIL>");

  //   await page.getByRole("button", { name: "Complete Order" }).tap();

  //   // Handle any dialogs that might appear
  //   page.once("dialog", (dialog) => {
  //     dialog.dismiss().catch(() => {});
  //   });

  //   await page.waitForTimeout(config.timeouts.medium);

  //   const response = await fetch("http://localhost:1080/api/messages");
  //   const messages = await response.json();
  //   expect(messages.length).toBeGreaterThan(0);
  //   const lastMessage = messages[messages.length - 1];
  //   expect(lastMessage.to[0].email).toBe("<EMAIL>");
  //   expect(lastMessage.subject).toBe("Order Confirmation");
  // });
});
