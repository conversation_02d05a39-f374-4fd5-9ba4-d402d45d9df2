import { expect, type Locator, type Page } from "@playwright/test";

export const getMoreSelectionsButton = (locator: Locator | Page) =>
  locator.getByRole("button", {
    name: /Choose selections|Selections required/,
  });

export const openItemDialog = async (page: Page, itemName: string) => {
  const items = page.getByRole("region", { name: "items" });

  const itemButton = items
    .getByRole("button")
    .getByRole("heading", { name: itemName, exact: true });
  await itemButton.click();

  const dialog = page.getByRole("dialog");

  await expect(dialog).toBeVisible();

  return dialog;
};

export const getCart = async (page: Page, isMobile: boolean) => {
  let cart = page.getByRole("region", { name: "Cart", exact: true });

  if ((await cart.count()) === 0 && isMobile) {
    await page.getByRole("button", { name: "View Cart" }).click();
    cart = page.getByRole("region", { name: "Cart", exact: true });
  }

  await expect(cart).toBeVisible();
  return cart;
};

export const selectItemAndAddToCart = async (page: Page, itemName: string) => {
  const dialog = await openItemDialog(page, itemName);

  await dialog.getByRole("button", { name: "Add to Cart" }).click();
};

export const selectModifiers = async ({
  locator,
  modifiersToSelect,
}: {
  locator: Locator;
  modifiersToSelect: string[];
}) => {
  for (const modifier of modifiersToSelect) {
    const checkbox = locator.getByRole("checkbox", {
      name: modifier,
      exact: true,
    });

    await expect(checkbox).toBeVisible();
    await checkbox.click();
  }
};

export const addWithModifier = async ({
  page,
  itemName,
  modifiersToSelect,
}: {
  page: Page;
  itemName: string;
  modifiersToSelect: string[];
}) => {
  const dialog = await openItemDialog(page, itemName);

  await selectModifiers({ locator: dialog, modifiersToSelect });

  await dialog.getByRole("button", { name: "Add to cart" }).click();
  await expect(dialog).not.toBeVisible();
};

export const checkItemInCart = async ({
  page,
  itemName,
  modifiers,
  isMobile,
}: {
  page: Page;
  itemName: string;
  modifiers: string[];
  isMobile: boolean;
}) => {
  const cart = await getCart(page, isMobile);

  await expect(
    cart.locator(`[data-testId="cart-item-${itemName}"]`),
  ).toBeVisible();

  for (const modifier of modifiers) {
    await expect(cart.getByText(modifier)).toBeVisible();
  }

  if (isMobile) {
    await page.getByRole("button", { name: "Close", exact: true }).click();
  }
};

export const findCartItem = async (
  page: Page,
  itemName: string,
  isMobile: boolean,
) => {
  const cart = await getCart(page, isMobile);

  // Get all cart list items
  const cartListItems = cart.getByRole("listitem");

  // For the filter to work correctly with 'has', we need to use a relative locator
  // The key insight from Playwright docs: the inner locator for 'has' must be relative
  const item = cartListItems
    .filter({
      has: page.locator(`[data-testId="cart-item-${itemName}"]`),
    })
    .first();

  await expect(item).toBeVisible();
  return item;
};

export const updateSelectedModifiersInCart = async (
  page: Page,
  itemName: string,
  modifiers: string[],
  isMobile: boolean,
) => {
  const item = await findCartItem(page, itemName, isMobile);

  // Use role to find the edit button within the item
  await item.getByRole("button", { name: "Edit" }).click();

  const dialog = page.getByRole("dialog", { name: itemName });

  expect(await dialog.count()).toEqual(1);

  while ((await dialog.locator('input[type="checkbox"]:checked').count()) > 0) {
    const checkbox = dialog.locator('input[type="checkbox"]:checked').first();
    await checkbox.click();
  }

  for (const modifier of modifiers) {
    const checkbox = dialog.getByRole("checkbox", {
      name: modifier,
      exact: true,
    });
    await expect(checkbox).toBeVisible();
    await checkbox.click();
  }

  await dialog.getByRole("button", { name: "Update Item" }).click();
  await expect(dialog).not.toBeVisible();
};
