import { devices, type PlaywrightTestConfig } from "@playwright/test";

const config: PlaywrightTestConfig = {
  retries: 1,
  webServer: {
    command: "npm run build && npm run preview",
    port: 4173,
    stdout: "pipe",
    stderr: "pipe",
  },
  timeout: 45 * 1000,
  reporter: "html",
  use: {
    screenshot: "only-on-failure",
    // 'on-first-retry' speeds up tests vs 'retain-on-failure', especially on webkit.
    trace: "on-first-retry",
    video: "on-first-retry",
  },
  testDir: "tests",
  testMatch: /(.+\.)?(test|spec)\.[jt]s/,
  projects: [
    {
      name: "Chromium",
      use: {
        ...devices["Desktop Chrome"],
      },
    },
    {
      name: "Mobile Safari",
      use: {
        ...devices["iPhone 12 Pro"],
        contextOptions: {
          reducedMotion: "reduce",
        },
      },
    },
    {
      name: "Mobile Chrome",
      use: {
        ...devices["Pixel 7"],
        contextOptions: {
          reducedMotion: "reduce",
        },
      },
    },
  ],
};

export default config;
